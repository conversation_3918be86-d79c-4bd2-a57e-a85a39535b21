/**
 * 타입 안전한 세션 스토리지 유틸리티
 */

type StorageValue = string | number | boolean | object | null;

// 인증 관련 키 상수
export const AUTH_KEYS = {
  TOKEN: 'token',
  USER_IDX: 'userIdx',
  IS_ADMIN: 'isAdmin',
  IS_APPLY: 'isApply',
  IS_LOGIN: 'isLogin',
  USER_ID: 'userId',
} as const;

export const sessionStorage = {
  /**
   * 값을 세션 스토리지에 저장
   */
  set: (key: string, value: StorageValue): boolean => {
    try {
      if (typeof window === 'undefined') {
        console.warn('sessionStorage is not available in this environment');
        return false;
      }

      console.log(`💾 Setting sessionStorage[${key}]:`, value);

      const serializedValue = JSON.stringify(value);

      // 세션 스토리지 용량 확인
      try {
        const testKey = '__test__';
        window.sessionStorage.setItem(testKey, 'test');
        window.sessionStorage.removeItem(testKey);
      } catch (e) {
        console.error('💾 SessionStorage quota exceeded or unavailable');
        return false;
      }

      window.sessionStorage.setItem(key, serializedValue);

      // 저장 확인
      const saved = window.sessionStorage.getItem(key);
      const success = saved === serializedValue;

      console.log(
        `💾 Save verification for ${key}:`,
        success,
        saved ? 'Data saved' : 'No data found'
      );

      return success;
    } catch (error) {
      console.error(`💾 Failed to save to sessionStorage[${key}]:`, error);
      return false;
    }
  },

  /**
   * 세션 스토리지에서 값을 가져옴
   */
  get: <T = StorageValue>(key: string): T | null => {
    try {
      if (typeof window === 'undefined') {
        return null;
      }

      const item = window.sessionStorage.getItem(key);
      if (item === null) return null;
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('Failed to get from sessionStorage:', error);
      return null;
    }
  },

  /**
   * 세션 스토리지에서 특정 키 제거
   */
  remove: (key: string): boolean => {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      window.sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Failed to remove from sessionStorage:', error);
      return false;
    }
  },

  /**
   * 세션 스토리지 전체 클리어
   */
  clear: (): boolean => {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      window.sessionStorage.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear sessionStorage:', error);
      return false;
    }
  },

  /**
   * 특정 키가 존재하는지 확인
   */
  has: (key: string): boolean => {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      return window.sessionStorage.getItem(key) !== null;
    } catch (error) {
      return false;
    }
  },

  /**
   * 인증 정보 저장
   */
  setAuthData: (authData: {
    token: string;
    userIdx: number;
    isAdmin: number;
    isApply: number;
    userId: string;
  }): boolean => {
    try {
      console.log('💾 Attempting to save auth data:', authData);

      // 각각 개별적으로 저장하고 결과 확인
      const tokenResult = sessionStorage.set(AUTH_KEYS.TOKEN, authData.token);
      console.log('💾 Token save result:', tokenResult);

      const userIdxResult = sessionStorage.set(
        AUTH_KEYS.USER_IDX,
        authData.userIdx
      );
      console.log('💾 UserIdx save result:', userIdxResult);

      const isAdminResult = sessionStorage.set(
        AUTH_KEYS.IS_ADMIN,
        authData.isAdmin
      );
      console.log('💾 IsAdmin save result:', isAdminResult);

      const isApplyResult = sessionStorage.set(
        AUTH_KEYS.IS_APPLY,
        authData.isApply
      );
      console.log('💾 IsApply save result:', isApplyResult);

      const userIdResult = sessionStorage.set(
        AUTH_KEYS.USER_ID,
        authData.userId
      );
      console.log('💾 UserId save result:', userIdResult);

      const isLoginResult = sessionStorage.set(AUTH_KEYS.IS_LOGIN, true);
      console.log('💾 IsLogin save result:', isLoginResult);

      const success = [
        tokenResult,
        userIdxResult,
        isAdminResult,
        isApplyResult,
        userIdResult,
        isLoginResult,
      ].every(Boolean);

      console.log('💾 Overall save success:', success);

      // 저장 후 즉시 확인
      const savedData = sessionStorage.getAuthData();
      console.log('💾 Saved data verification:', savedData);

      return success;
    } catch (error) {
      console.error('💾 Failed to set auth data:', error);
      return false;
    }
  },

  /**
   * 인증 정보 가져오기
   */
  getAuthData: () => {
    return {
      token: sessionStorage.get<string>(AUTH_KEYS.TOKEN),
      userIdx: sessionStorage.get<number>(AUTH_KEYS.USER_IDX),
      isAdmin: sessionStorage.get<number>(AUTH_KEYS.IS_ADMIN),
      isApply: sessionStorage.get<number>(AUTH_KEYS.IS_APPLY),
      userId: sessionStorage.get<string>(AUTH_KEYS.USER_ID),
      isLogin: sessionStorage.get<boolean>(AUTH_KEYS.IS_LOGIN),
    };
  },

  /**
   * 인증 정보 클리어
   */
  clearAuthData: (): boolean => {
    try {
      const keys = Object.values(AUTH_KEYS);
      const success = keys
        .map(key => sessionStorage.remove(key))
        .every(Boolean);
      return success;
    } catch (error) {
      console.error('Failed to clear auth data:', error);
      return false;
    }
  },
};
