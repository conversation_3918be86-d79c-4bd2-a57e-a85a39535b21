/**
 * 유효성 검사 유틸리티 함수들
 */

/**
 * 값이 비어있는지 확인
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * 이메일 형식 검증
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[0-9a-zA-Z]([-_\.]?[0-9a-zA-Z])*@[0-9a-zA-Z]([-_\.]?[0-9a-zA-Z])*\.[a-zA-Z]{2,3}$/i;
  return emailRegex.test(email);
};

/**
 * 휴대폰 번호 형식 검증
 */
export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^01(?:0|1|[6-9])-(?:\d{3}|\d{4})-\d{4}$/;
  return phoneRegex.test(phone);
};

/**
 * 비밀번호 강도 검증
 */
export const isValidPassword = (password: string): boolean => {
  // 최소 8자, 대소문자, 숫자, 특수문자 포함
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

/**
 * 사용자 ID 형식 검증
 */
export const isValidUserId = (userId: string): boolean => {
  // 4-20자, 영문자로 시작, 영문자와 숫자만 허용
  const userIdRegex = /^[a-zA-Z][a-zA-Z0-9]{3,19}$/;
  return userIdRegex.test(userId);
};

/**
 * 필수 필드 검증
 */
export const validateRequiredFields = (
  data: Record<string, any>,
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } => {
  const missingFields = requiredFields.filter(field => isEmpty(data[field]));
  return {
    isValid: missingFields.length === 0,
    missingFields,
  };
};

/**
 * 폼 데이터 검증
 */
export const validateFormData = (
  data: Record<string, any>,
  rules: Record<string, (value: any) => boolean>
): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  Object.entries(rules).forEach(([field, validator]) => {
    if (!validator(data[field])) {
      errors[field] = `Invalid ${field}`;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
