import { format, parseISO } from 'date-fns';
import { ko } from 'date-fns/locale';

/**
 * 날짜 포맷팅 유틸리티
 */
export const formatDate = (
  date: string | Date | null | undefined,
  formatString = 'yyyy년 MM월 dd일',
  locale = ko
): string => {
  try {
    if (!date) return '-';

    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    // 유효한 날짜인지 확인
    if (isNaN(dateObj.getTime())) {
      return '-';
    }

    return format(dateObj, formatString, { locale });
  } catch (error) {
    console.error('Date formatting error:', error);
    return '-';
  }
};

/**
 * 상대적 시간 표시
 */
export const formatRelativeTime = (
  date: string | Date | null | undefined
): string => {
  try {
    if (!date) return '-';

    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    // 유효한 날짜인지 확인
    if (isNaN(dateObj.getTime())) {
      return '-';
    }

    const now = new Date();
    const diffInMs = now.getTime() - dateObj.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) return '방금 전';
    if (diffInMinutes < 60) return `${diffInMinutes}분 전`;
    if (diffInHours < 24) return `${diffInHours}시간 전`;
    if (diffInDays < 7) return `${diffInDays}일 전`;

    return formatDate(dateObj, 'yyyy.MM.dd');
  } catch (error) {
    console.error('Relative time formatting error:', error);
    return '-';
  }
};

/**
 * 파일 크기 포맷팅
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 전화번호 포맷팅
 */
export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{3})(\d{3,4})(\d{4})$/);

  if (match) {
    return `${match[1]}-${match[2]}-${match[3]}`;
  }

  return phone;
};

/**
 * 숫자에 천 단위 콤마 추가
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString('ko-KR');
};

/**
 * 문자열 자르기 (말줄임표 추가)
 */
export const truncateString = (str: string, maxLength: number): string => {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength) + '...';
};
