import {
  isEmpty,
  isValidEmail,
  isValidPhoneNumber,
  isValidPassword,
  isValidUserId,
  validateRequiredFields,
  validateFormData,
} from '../validation';

describe('validation utils', () => {
  describe('isEmpty', () => {
    it('returns true for null and undefined', () => {
      expect(isEmpty(null)).toBe(true);
      expect(isEmpty(undefined)).toBe(true);
    });

    it('returns true for empty string', () => {
      expect(isEmpty('')).toBe(true);
      expect(isEmpty('   ')).toBe(true);
    });

    it('returns true for empty array', () => {
      expect(isEmpty([])).toBe(true);
    });

    it('returns true for empty object', () => {
      expect(isEmpty({})).toBe(true);
    });

    it('returns false for non-empty values', () => {
      expect(isEmpty('hello')).toBe(false);
      expect(isEmpty([1, 2, 3])).toBe(false);
      expect(isEmpty({ key: 'value' })).toBe(false);
      expect(isEmpty(0)).toBe(false);
      expect(isEmpty(false)).toBe(false);
    });
  });

  describe('isValidEmail', () => {
    it('returns true for valid emails', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('returns false for invalid emails', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@domain.com')).toBe(false);
      expect(isValidEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('isValidPhoneNumber', () => {
    it('returns true for valid phone numbers', () => {
      expect(isValidPhoneNumber('010-1234-5678')).toBe(true);
      expect(isValidPhoneNumber('************')).toBe(true);
      expect(isValidPhoneNumber('016-1234-5678')).toBe(true);
    });

    it('returns false for invalid phone numbers', () => {
      expect(isValidPhoneNumber('010-12345-678')).toBe(false);
      expect(isValidPhoneNumber('010-1234-56789')).toBe(false);
      expect(isValidPhoneNumber('020-1234-5678')).toBe(false);
      expect(isValidPhoneNumber('01012345678')).toBe(false);
    });
  });

  describe('isValidPassword', () => {
    it('returns true for valid passwords', () => {
      expect(isValidPassword('Password123!')).toBe(true);
      expect(isValidPassword('MySecure@Pass1')).toBe(true);
    });

    it('returns false for invalid passwords', () => {
      expect(isValidPassword('password')).toBe(false); // no uppercase, number, special char
      expect(isValidPassword('PASSWORD')).toBe(false); // no lowercase, number, special char
      expect(isValidPassword('Password')).toBe(false); // no number, special char
      expect(isValidPassword('Pass1!')).toBe(false); // too short
    });
  });

  describe('isValidUserId', () => {
    it('returns true for valid user IDs', () => {
      expect(isValidUserId('user123')).toBe(true);
      expect(isValidUserId('testUser')).toBe(true);
      expect(isValidUserId('a1b2c3d4')).toBe(true);
    });

    it('returns false for invalid user IDs', () => {
      expect(isValidUserId('123user')).toBe(false); // starts with number
      expect(isValidUserId('usr')).toBe(false); // too short
      expect(isValidUserId('user_name')).toBe(false); // contains underscore
      expect(isValidUserId('user-name')).toBe(false); // contains hyphen
    });
  });

  describe('validateRequiredFields', () => {
    it('returns valid when all required fields are present', () => {
      const data = { name: 'John', email: '<EMAIL>' };
      const required = ['name', 'email'];
      
      const result = validateRequiredFields(data, required);
      
      expect(result.isValid).toBe(true);
      expect(result.missingFields).toEqual([]);
    });

    it('returns invalid when required fields are missing', () => {
      const data = { name: 'John' };
      const required = ['name', 'email', 'phone'];
      
      const result = validateRequiredFields(data, required);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['email', 'phone']);
    });
  });

  describe('validateFormData', () => {
    it('returns valid when all rules pass', () => {
      const data = { email: '<EMAIL>', age: 25 };
      const rules = {
        email: (value: string) => isValidEmail(value),
        age: (value: number) => value >= 18,
      };
      
      const result = validateFormData(data, rules);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('returns invalid when rules fail', () => {
      const data = { email: 'invalid-email', age: 15 };
      const rules = {
        email: (value: string) => isValidEmail(value),
        age: (value: number) => value >= 18,
      };
      
      const result = validateFormData(data, rules);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toEqual({
        email: 'Invalid email',
        age: 'Invalid age',
      });
    });
  });
});
