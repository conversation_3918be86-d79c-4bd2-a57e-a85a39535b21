import { apiRequest } from './client';
import { LoginRequest, LoginResponse, BaseResponse } from '@/types';

export const authApi = {
  // 로그인
  login: (data: LoginRequest): Promise<LoginResponse> =>
    apiRequest.post('/users/login', data),

  // 회원가입
  register: (data: any): Promise<BaseResponse> =>
    apiRequest.post('/users/addUser', data),

  // 로그아웃
  logout: (): Promise<BaseResponse> => apiRequest.post('/users/logout'),
};
