import { apiRequest } from './client';
import {
  BaseResponse,
  SignDocument,
  SignDetailResponse,
  CreateSignRequest,
  UpdateSignRequest,
} from '@/types';

export const signaturesApi = {
  // 서명 문서 목록 조회
  getList: (queryParams = ''): Promise<BaseResponse<SignDocument[]>> =>
    apiRequest.get(`/admin/signatureList${queryParams}`),

  // 서명 문서 상세 조회 (관리자)
  getDetail: (params: string): Promise<SignDetailResponse> =>
    apiRequest.get(`/admin/getDetail${params}`),

  // 서명 문서 상세 조회 (사용자)
  getDetailForUser: (key: string): Promise<SignDetailResponse> =>
    apiRequest.get(`/users/getDetail/${key}`),

  // COVID-19 문서 상세 조회
  getCovidDetail: (idx: number): Promise<BaseResponse> =>
    apiRequest.get(`/admin/signSurveryDetail?idx=${idx}`),

  // 사용자 조사 문서 상세 조회
  getUserSurveyDetail: (idx: number): Promise<BaseResponse> =>
    apiRequest.get(`/admin/signPrivacyDetail?idx=${idx}`),

  // 새 서명 문서 생성
  createSign: (data: CreateSignRequest): Promise<BaseResponse> =>
    apiRequest.post('/admin/addSign', data),

  // 개인정보 관련 서명 추가
  addPrivacySign: (data: any): Promise<BaseResponse> =>
    apiRequest.post('/admin/signPrivacyAdd', data),

  // 서명 문서 업데이트 (관리자)
  updateSign: (data: any): Promise<BaseResponse> =>
    apiRequest.post('/admin/updateSign', data),

  // 서명 문서 업데이트 (사용자)
  updateUserSign: (data: UpdateSignRequest): Promise<BaseResponse> =>
    apiRequest.post('/users/updateSign', data),

  // 서명 문서 삭제
  deleteSign: (data: { idx: number }): Promise<BaseResponse> =>
    apiRequest.post('/admin/deleteSign', data),
};
