import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { sessionStorage } from '@/utils/sessionStorage';

// API 에러 타입 정의
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// API 클라이언트 설정
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      const token = sessionStorage.get<string>('token');
      if (token && config.headers) {
        // Bearer 형식으로 토큰 전송
        config.headers.Authorization = `Bearer ${token}`;

        console.log('🔑 Token being sent:', token ? `${token.substring(0, 20)}...` : 'None');
      }

      // 개발 환경에서 요청 로깅
      if (import.meta.env.DEV) {
        console.log('🚀 API Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          headers: {
            Authorization: config.headers?.Authorization ? '***' : 'None',
          },
          data: config.data,
        });
      }

      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // 개발 환경에서 응답 로깅
      if (import.meta.env.DEV) {
        console.log('✅ API Response:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
        });
      }

      return response;
    },
    (error: AxiosError) => {
      // 에러 로깅
      console.error('❌ API Error:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.message,
        data: error.response?.data,
      });

      // 401 Unauthorized - 토큰 만료 또는 인증 실패
      if (error.response?.status === 401) {
        console.log('🚫 401 Unauthorized - Token expired or invalid');
        // 세션 클리어는 하지만 자동 리다이렉트는 하지 않음
        // 컴포넌트 레벨에서 처리하도록 함
        sessionStorage.clear();
      }

      // 403 Forbidden - 권한 없음
      if (error.response?.status === 403) {
        console.error('🚫 403 Forbidden Error - API Access Denied:', {
          url: error.config?.url,
          method: error.config?.method,
          data: error.response?.data
        });
        // 임시로 자동 리다이렉트 비활성화
        // window.location.href = '/unauthorized';
      }

      // 500 Internal Server Error
      if (error.response?.status === 500) {
        console.error('서버 내부 오류가 발생했습니다.');
      }

      // 네트워크 오류
      if (!error.response) {
        console.error('네트워크 연결을 확인해주세요.');
      }

      return Promise.reject(createApiError(error));
    }
  );

  return client;
};

// API 에러 객체 생성
const createApiError = (error: AxiosError): ApiError => {
  const response = error.response;

  if (response?.data && typeof response.data === 'object') {
    const data = response.data as any;
    return {
      message: data.message || data.error || '알 수 없는 오류가 발생했습니다.',
      status: response.status,
      code: data.code,
    };
  }

  return {
    message: error.message || '네트워크 오류가 발생했습니다.',
    status: response?.status,
  };
};

export const apiClient = createApiClient();

// Generic API 함수들
export const apiRequest = {
  get: <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.get(url, config).then(response => response.data),

  post: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, data, config).then(response => response.data),

  put: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.put(url, data, config).then(response => response.data),

  delete: <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.delete(url, config).then(response => response.data),
};
