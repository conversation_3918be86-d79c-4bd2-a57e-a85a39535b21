import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { signaturesApi } from '@/api/signatures';
import {
  SignDocument,
  SignDetailResponse,
  CreateSignRequest,
  UpdateSignRequest,
} from '@/types';

interface SignaturesState {
  list: SignDocument[];
  currentDocument: SignDetailResponse | null;
  viewerDocument: SignDetailResponse | null;
  signImages: string[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: SignaturesState = {
  list: [],
  currentDocument: null,
  viewerDocument: null,
  signImages: [],
  isLoading: false,
  error: null,
  pagination: {
    page: 0,
    limit: 10,
    total: 0,
  },
};

// Async thunks
export const fetchSignaturesList = createAsyncThunk(
  'signatures/fetchList',
  async (queryParams: string = '', { rejectWithValue }) => {
    try {
      const response = await signaturesApi.getList(queryParams);
      return response.result;
    } catch (error: any) {
      return rejectWithValue(error.message || '목록을 불러오는데 실패했습니다.');
    }
  }
);

export const fetchSignatureDetail = createAsyncThunk(
  'signatures/fetchDetail',
  async (params: string, { rejectWithValue }) => {
    try {
      const response = await signaturesApi.getDetail(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || '상세 정보를 불러오는데 실패했습니다.');
    }
  }
);

export const fetchSignatureForViewer = createAsyncThunk(
  'signatures/fetchForViewer',
  async (key: string, { rejectWithValue }) => {
    try {
      const response = await signaturesApi.getDetailForUser(key);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || '문서를 불러오는데 실패했습니다.');
    }
  }
);

export const createSignature = createAsyncThunk(
  'signatures/create',
  async (data: CreateSignRequest, { rejectWithValue }) => {
    try {
      const response = await signaturesApi.createSign(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || '문서 생성에 실패했습니다.');
    }
  }
);

export const updateSignature = createAsyncThunk(
  'signatures/update',
  async (data: UpdateSignRequest, { rejectWithValue }) => {
    try {
      const response = await signaturesApi.updateUserSign(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || '문서 업데이트에 실패했습니다.');
    }
  }
);

export const deleteSignature = createAsyncThunk(
  'signatures/delete',
  async (idx: number, { rejectWithValue }) => {
    try {
      const response = await signaturesApi.deleteSign({ idx });
      return { response, idx };
    } catch (error: any) {
      return rejectWithValue(error.message || '문서 삭제에 실패했습니다.');
    }
  }
);

const signaturesSlice = createSlice({
  name: 'signatures',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    saveSignImage: (state, action: PayloadAction<{ index: number; image: string }>) => {
      const { index, image } = action.payload;
      state.signImages[index] = image;
    },
    clearSignImages: (state) => {
      state.signImages = [];
    },
    setPagination: (state, action: PayloadAction<Partial<SignaturesState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearCurrentDocument: (state) => {
      state.currentDocument = null;
    },
    clearViewerDocument: (state) => {
      state.viewerDocument = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch list
      .addCase(fetchSignaturesList.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSignaturesList.fulfilled, (state, action) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(fetchSignaturesList.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch detail
      .addCase(fetchSignatureDetail.fulfilled, (state, action) => {
        state.currentDocument = action.payload;
      })
      // Fetch for viewer
      .addCase(fetchSignatureForViewer.fulfilled, (state, action) => {
        state.viewerDocument = action.payload;
      })
      // Create
      .addCase(createSignature.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSignature.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createSignature.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateSignature.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSignature.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateSignature.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteSignature.fulfilled, (state, action) => {
        const { idx } = action.payload;
        state.list = state.list.filter(item => item.idx !== idx);
      });
  },
});

export const {
  clearError,
  saveSignImage,
  clearSignImages,
  setPagination,
  clearCurrentDocument,
  clearViewerDocument,
} = signaturesSlice.actions;

export default signaturesSlice.reducer;
