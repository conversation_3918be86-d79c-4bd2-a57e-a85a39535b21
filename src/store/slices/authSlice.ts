import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authApi } from '@/api/auth';
import { sessionStorage } from '@/utils/sessionStorage';
import { User, LoginRequest } from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const loginAsync = createAsyncThunk(
  'auth/login',
  async (credentials: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await authApi.login(credentials);

      console.log('🔐 Login Response:', response);

      // 응답 구조 확인 및 처리
      const userData = response.data || response.result || response;

      console.log('🔐 User Data:', userData);

      if (userData.status === 'success' || userData.user_idx) {
        // 세션 스토리지에 저장
        const authData = {
          token: userData.jsontoken || userData.token,
          userIdx: userData.user_idx,
          isAdmin: userData.is_admin,
          isApply: userData.is_apply,
          userId: userData.user_id,
        };

        console.log('🔐 Auth Data to Save:', authData);

        const saveResult = sessionStorage.setAuthData(authData);
        console.log('🔐 Save Result:', saveResult);

        return userData;
      } else {
        console.error('🔐 Login Failed - Invalid Response:', userData);
        return rejectWithValue(userData.message || '로그인에 실패했습니다.');
      }
    } catch (error: any) {
      console.error('🔐 Login API Error:', error);
      return rejectWithValue(error.message || '로그인 중 오류가 발생했습니다.');
    }
  }
);

export const logoutAsync = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authApi.logout();
      sessionStorage.clear();
      return null;
    } catch (error: any) {
      // 로그아웃은 실패해도 로컬 상태는 클리어
      sessionStorage.clear();
      return null;
    }
  }
);

export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async (_, { rejectWithValue }) => {
    try {
      const authData = sessionStorage.getAuthData();

      console.log('🔄 Initializing auth with data:', authData);

      if (authData.isLogin && authData.token) {
        // 클라이언트 사이드 토큰 검증 (JWT 디코딩)
        try {
          const payload = JSON.parse(atob(authData.token.split('.')[1]));
          const currentTime = Date.now() / 1000;

          if (payload.exp && payload.exp > currentTime) {
            // 토큰이 유효한 경우 세션 데이터로 사용자 정보 복원
            console.log('🔄 Token is valid, restoring user data');

            return {
              user_idx: authData.userIdx || 0,
              user_id: authData.userId || '',
              is_admin: authData.isAdmin || 0,
              is_apply: authData.isApply || 1, // 세션에서 실제 값 사용
              status: 'success' as const,
              jsontoken: authData.token,
            };
          } else {
            console.log('🔄 Token expired, clearing session');
            sessionStorage.clearAuthData();
            return null;
          }
        } catch (tokenError) {
          console.error('🔄 Token validation failed:', tokenError);
          sessionStorage.clearAuthData();
          return null;
        }
      }

      console.log('🔄 No valid session found');
      return null;
    } catch (error) {
      console.error('🔄 Auth initialization error:', error);
      sessionStorage.clearAuthData();
      return null;
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
  },
  extraReducers: builder => {
    builder
      // Login
      .addCase(loginAsync.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
      })
      // Logout
      .addCase(logoutAsync.fulfilled, state => {
        state.user = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      // Initialize
      .addCase(initializeAuth.fulfilled, (state, action) => {
        if (action.payload) {
          state.user = action.payload;
          state.isAuthenticated = true;
        }
      });
  },
});

export const { clearError, setUser } = authSlice.actions;
export default authSlice.reducer;
