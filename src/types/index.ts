// Common types
export interface BaseResponse<T = any> {
  result: T;
  status: 'success' | 'error';
  message?: string;
}

// User types
export interface User {
  user_idx: number;
  user_id: string;
  user_name?: string;
  email?: string;
  is_admin: number;
  is_apply: number;
  status: 'success' | 'error';
  jsontoken?: string;
}

export interface LoginRequest {
  user_id: string;
  user_passwd: string;
}

export interface LoginResponse extends BaseResponse<User> {
  data: User;
}

// Sign types
export interface SignDocument {
  idx: number;
  sign_type: 'sign' | 'survey';
  project: string;
  major_nm: string;
  minor_nm: string;
  officer_nm: string;
  officer_mail?: string;
  language: 'ko' | 'en';
  sign_status: string; // 서버에서 "false", "true" 등의 문자열로 반환
  create_date?: string;
  update_date?: string;
  url_key: string; // 서버에서 url_key로 반환
  sign_key?: string; // 호환성을 위해 선택적으로 유지
}

export interface SignSection {
  idx: number;
  section_nm: string;
  section_flag: number;
  sign_img?: string;
}

export interface SignDetailResponse extends BaseResponse {
  results: SignDocument;
  section: SignSection[];
  type: 'sign' | 'survey';
}

export interface CreateSignRequest {
  sign_type: 'sign' | 'survey';
  project: string;
  major_nm: string;
  minor_nm: string;
  officer_nm: string;
  officer_mail: string;
  language: 'ko' | 'en';
  section_data: Array<{
    section_nm: string;
    section_flag: number;
  }>;
  user_idx: number;
}

export interface UpdateSignRequest {
  idx: number;
  type: 'sign' | 'survey';
  account?: any;
  sectionList?: Array<{
    idx: number;
    sign_img: string | null;
  }>;
  sign_img?: string;
  section_idx?: number;
  question?: any;
}

// UI types
export interface UIState {
  header: boolean;
  footer: boolean;
}

// Loading types
export interface LoadingState {
  [key: string]: boolean;
}

// Route types
export interface RouteParams {
  key?: string;
  [key: string]: string | undefined;
}

// Form types
export interface FormFieldProps {
  name: string;
  label: string;
  required?: boolean;
  type?: 'text' | 'email' | 'password' | 'tel';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
}

// Template types
export interface TemplateProps {
  mode?: 'write' | 'view' | 'viewer' | 'print';
  userName?: string;
  pxdName?: string;
  email?: string;
  date?: string;
  projectName?: string;
  clientName?: string;
  signImg?: string[];
  onSignSave?: (index: number, signData: string) => void;
}

// COVID19 specific types
export interface COVID19Question {
  question: string;
  answer: 'yes' | 'no' | '';
}

export interface COVID19Data {
  questions: COVID19Question[];
  sign_img: string;
}
