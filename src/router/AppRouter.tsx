import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import ProtectedRoute from '@/components/router/ProtectedRoute';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { useAppSelector } from '@/store';

// Lazy load components for better performance
const LoginPage = React.lazy(() => import('@/pages/LoginPage'));
const RegisterPage = React.lazy(() => import('@/pages/RegisterPage'));
const DashboardPage = React.lazy(() => import('@/pages/DashboardPage'));
const SignatureListPage = React.lazy(() => import('@/pages/SignatureListPage'));
const CreateSignaturePage = React.lazy(
  () => import('@/pages/CreateSignaturePage')
);
const SignatureDetailPage = React.lazy(
  () => import('@/pages/SignatureDetailPage')
);
const SignatureViewerPage = React.lazy(
  () => import('@/pages/SignatureViewerPage')
);
const UserManagementPage = React.lazy(
  () => import('@/pages/UserManagementPage')
);
const CompletePage = React.lazy(() => import('@/pages/CompletePage'));
const NotFoundPage = React.lazy(() => import('@/pages/NotFoundPage'));
const UnauthorizedPage = React.lazy(() => import('@/pages/UnauthorizedPage'));
const ErrorPage = React.lazy(() => import('@/pages/ErrorPage'));

const AppRouter: React.FC = () => {
  const { header, footer } = useAppSelector(state => state.ui);

  return (
    <Suspense
      fallback={<LoadingSpinner fullScreen message="페이지 로딩 중..." />}
    >
      <Routes>
        {/* Public routes */}
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <Layout>
                <LoginPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/register"
          element={
            <ProtectedRoute requireAuth={false}>
              <Layout>
                <RegisterPage />
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* Viewer routes (no header/footer) */}
        <Route
          path="/viewer/:key"
          element={
            <Layout disableGutters>
              <SignatureViewerPage />
            </Layout>
          }
        />
        <Route
          path="/complete"
          element={
            <Layout>
              <CompletePage />
            </Layout>
          }
        />

        {/* Protected routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Navigate to="/list" replace />
            </ProtectedRoute>
          }
        />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Layout>
                <DashboardPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/list"
          element={
            <ProtectedRoute>
              <Layout>
                <SignatureListPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/create"
          element={
            <ProtectedRoute>
              <Layout>
                <CreateSignaturePage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/edit/:id"
          element={
            <ProtectedRoute>
              <Layout>
                <CreateSignaturePage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/view"
          element={
            <ProtectedRoute>
              <Layout>
                <SignatureDetailPage />
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* Admin only routes */}
        <Route
          path="/userList"
          element={
            <ProtectedRoute requireAdmin>
              <Layout>
                <UserManagementPage />
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* Error routes */}
        <Route
          path="/unauthorized"
          element={
            <Layout>
              <UnauthorizedPage />
            </Layout>
          }
        />
        <Route
          path="/error"
          element={
            <Layout>
              <ErrorPage />
            </Layout>
          }
        />

        {/* 404 */}
        <Route
          path="*"
          element={
            <Layout>
              <NotFoundPage />
            </Layout>
          }
        />
      </Routes>
    </Suspense>
  );
};

export default AppRouter;
