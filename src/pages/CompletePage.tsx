import React from 'react';
import { Box, Typography, Button, Container, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const CompleteContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '60vh',
}));

const CompletePaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(6),
  textAlign: 'center',
  maxWidth: 500,
  width: '100%',
}));

const CompletePage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <CompleteContainer maxWidth="sm">
      <CompletePaper elevation={3}>
        <CheckCircleIcon sx={{ fontSize: 80, color: 'success.main', mb: 3 }} />
        
        <Typography variant="h4" component="h1" gutterBottom>
          서명이 완료되었습니다
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          문서에 성공적으로 서명하였습니다. 
          서명된 문서는 관리자에게 전달되었습니다.
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          이 창을 닫으셔도 됩니다.
        </Typography>
        
        <Box mt={4}>
          <Button 
            variant="contained" 
            onClick={() => window.close()}
            sx={{ mr: 2 }}
          >
            창 닫기
          </Button>
          <Button 
            variant="outlined" 
            onClick={() => navigate('/')}
          >
            홈으로 가기
          </Button>
        </Box>
      </CompletePaper>
    </CompleteContainer>
  );
};

export default CompletePage;
