import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import BlockIcon from '@mui/icons-material/Block';

const UnauthorizedContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '60vh',
  textAlign: 'center',
}));

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <UnauthorizedContainer maxWidth="sm">
      <BlockIcon sx={{ fontSize: 120, color: 'error.main', mb: 2 }} />
      
      <Typography variant="h4" component="h1" gutterBottom>
        접근 권한이 없습니다
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        이 페이지에 접근할 권한이 없습니다. 
        관리자 권한이 필요한 페이지입니다.
      </Typography>
      
      <Box mt={4} display="flex" gap={2}>
        <Button variant="contained" onClick={() => navigate(-1)}>
          이전 페이지
        </Button>
        <Button variant="outlined" onClick={() => navigate('/')}>
          홈으로 가기
        </Button>
      </Box>
    </UnauthorizedContainer>
  );
};

export default UnauthorizedPage;
