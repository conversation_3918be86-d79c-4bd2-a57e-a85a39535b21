import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

const ErrorContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '60vh',
  textAlign: 'center',
}));

const ErrorPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <ErrorContainer maxWidth="sm">
      <ErrorOutlineIcon sx={{ fontSize: 120, color: 'error.main', mb: 2 }} />
      
      <Typography variant="h4" component="h1" gutterBottom>
        오류가 발생했습니다
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        예상치 못한 오류가 발생했습니다. 
        잠시 후 다시 시도해주세요.
      </Typography>
      
      <Box mt={4} display="flex" gap={2}>
        <Button variant="contained" onClick={() => window.location.reload()}>
          새로고침
        </Button>
        <Button variant="outlined" onClick={() => navigate('/')}>
          홈으로 가기
        </Button>
      </Box>
    </ErrorContainer>
  );
};

export default ErrorPage;
