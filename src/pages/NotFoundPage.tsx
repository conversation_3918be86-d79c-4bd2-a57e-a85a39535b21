import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

const NotFoundContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '60vh',
  textAlign: 'center',
}));

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <NotFoundContainer maxWidth="sm">
      <ErrorOutlineIcon sx={{ fontSize: 120, color: 'text.secondary', mb: 2 }} />
      
      <Typography variant="h1" component="h1" sx={{ fontSize: '6rem', fontWeight: 'bold', mb: 2 }}>
        404
      </Typography>
      
      <Typography variant="h4" component="h2" gutterBottom>
        페이지를 찾을 수 없습니다
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        요청하신 페이지가 존재하지 않거나 이동되었을 수 있습니다.
      </Typography>
      
      <Box mt={4} display="flex" gap={2}>
        <Button variant="contained" onClick={() => navigate(-1)}>
          이전 페이지
        </Button>
        <Button variant="outlined" onClick={() => navigate('/')}>
          홈으로 가기
        </Button>
      </Box>
    </NotFoundContainer>
  );
};

export default NotFoundPage;
