import React, { memo } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const ManagementContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  textAlign: 'center',
}));

const UserManagementPage: React.FC = memo(() => {
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        사용자 관리
      </Typography>

      <ManagementContainer elevation={2}>
        <Alert severity="info">
          사용자 관리 기능은 현재 개발 중입니다.
        </Alert>
        
        <Typography variant="body1" sx={{ mt: 2 }}>
          이 페이지에서는 다음 기능들을 제공할 예정입니다:
        </Typography>
        
        <Box component="ul" sx={{ textAlign: 'left', mt: 2, maxWidth: 400, mx: 'auto' }}>
          <li>사용자 목록 조회</li>
          <li>사용자 승인/거부</li>
          <li>권한 관리</li>
          <li>사용자 정보 수정</li>
          <li>사용자 활동 로그</li>
        </Box>
      </ManagementContainer>
    </Box>
  );
});

UserManagementPage.displayName = 'UserManagementPage';

export default UserManagementPage;
