import React, { memo, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip,
  Divider,
  Grid,
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import LinkIcon from '@mui/icons-material/Link';
import PrintIcon from '@mui/icons-material/Print';
import { useSignatureDetail } from '@/hooks/useSignatures';
import { useAuth } from '@/hooks/useAuth';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { formatDate } from '@/utils/format';
import moment from 'moment';
import 'moment/locale/ko';

// UserSurvey TSX 컴포넌트들 import
import UserSurvey01 from '@/components/templates/UserSurvey/UserSurvey01';
import UserSurvey01En from '@/components/templates/UserSurvey/UserSurvey01En';
import UserSurvey02 from '@/components/templates/UserSurvey/UserSurvey02';
import UserSurvey02En from '@/components/templates/UserSurvey/UserSurvey02En';
import UserSurvey03 from '@/components/templates/UserSurvey/UserSurvey03';
import UserSurvey03En from '@/components/templates/UserSurvey/UserSurvey03En';
import UserSurvey04 from '@/components/templates/UserSurvey/UserSurvey04';
import UserSurvey04En from '@/components/templates/UserSurvey/UserSurvey04En';

const DetailContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  maxWidth: 800,
  margin: '0 auto',
}));

const InfoSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

const InfoRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  marginBottom: theme.spacing(1),
  '& .label': {
    minWidth: 120,
    fontWeight: 'bold',
    color: theme.palette.text.secondary,
  },
  '& .value': {
    flex: 1,
  },
}));

const SignatureDetailPage: React.FC = memo(() => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAdmin } = useAuth();

  // URL 쿼리 파라미터에서 type과 idx 가져오기
  const searchParams = new URLSearchParams(location.search);
  const type = searchParams.get('type') || 'sign';
  const idx = searchParams.get('idx') || '';

  // location state에서 signKey 가져오기 (fallback)
  const signKey = (location.state as any)?.signKey || '';

  const { data, isLoading, error } = useSignatureDetail(
    idx,
    type,
    signKey,
    isAdmin
  );

  // 디버깅 정보 출력
  useEffect(() => {
    console.log('🔍 SignatureDetailPage Debug:', {
      idx,
      signKey,
      isAdmin,
      searchParams: location.search,
      data,
      error,
      isLoading,
    });
  }, [idx, signKey, isAdmin, location.search, data, error, isLoading]);

  // UserSurvey 컨텐츠 렌더링 함수 (기존 UserSurveyViewContainer.js 로직)
  const renderUserSurveyContent = () => {
    // 서버 응답에서 section_data 사용
    const sectionData = (data as any)?.section_data;
    if (!sectionData || !Array.isArray(sectionData)) {
      return (
        <Box p={2} bgcolor="yellow.50" borderRadius={1}>
          <Typography variant="body2" color="text.secondary">
            섹션 데이터가 없습니다. (section_data가 없거나 배열이 아님)
          </Typography>
        </Box>
      );
    }

    const results = (data as any)?.results;
    const account = (data as any)?.account;
    const lang = results.language;

    // 섹션 데이터를 기존 방식으로 구성 (서버 응답 구조에 맞게)
    // 서버에서 section_nm이 null로 오는 경우를 대비해 기본 이름 매핑
    const sectionNames = {
      0:
        lang === 'en'
          ? 'Personal Information Collection and Use Consent Form'
          : '개인정보 수집 및 이용 동의서',
      1:
        lang === 'en'
          ? 'User Research Participation Consent Form'
          : '사용자조사 참여 동의서',
      2:
        lang === 'en'
          ? 'Participation Fee Payment Information'
          : '참여 수당 지급 정보',
      3:
        lang === 'en'
          ? 'Final Confirmation and Consent Form'
          : '최종 확인 및 동의서',
    };

    const section = {
      UserSurvey01: sectionData[0]
        ? {
            ...sectionData[0],
            section_nm: sectionData[0].section_nm || sectionNames[0],
          }
        : null,
      UserSurvey02: sectionData[1]
        ? {
            ...sectionData[1],
            section_nm: sectionData[1].section_nm || sectionNames[1],
          }
        : null,
      UserSurvey03: sectionData[2]
        ? {
            ...sectionData[2],
            section_nm: sectionData[2].section_nm || sectionNames[2],
          }
        : null,
      UserSurvey04: sectionData[3]
        ? {
            ...sectionData[3],
            section_nm: sectionData[3].section_nm || sectionNames[3],
          }
        : null,
    };

    const userName = results.minor_nm;
    const pxdName = results.officer_nm;
    const email = results.officer_mail;
    const projectName = results.project;
    const clientName = results.major_nm;
    const date = moment(results.create_date).format('LL');
    const accountData =
      section.UserSurvey03?.section_flag === 1 ? account?.[0] : null;

    return (
      <Box>
        {/* 실제 UserSurvey 컴포넌트들 렌더링 */}
        {/* UserSurvey01 */}
        {section.UserSurvey01?.section_flag === 1 && lang === 'en' && (
          <UserSurvey01En
            mode="view"
            idx={section.UserSurvey01.section_idx}
            userName={userName}
            pxdName={pxdName}
            email={email}
            date={date}
            signImg={section.UserSurvey01.sign_img}
          />
        )}
        {section.UserSurvey01?.section_flag === 1 && lang === 'ko' && (
          <UserSurvey01
            mode="view"
            idx={section.UserSurvey01.section_idx}
            userName={userName}
            pxdName={pxdName}
            email={email}
            date={date}
            signImg={section.UserSurvey01.sign_img}
          />
        )}

        {/* UserSurvey02 */}
        {section.UserSurvey02?.section_flag === 1 && lang === 'en' && (
          <UserSurvey02En
            mode="view"
            idx={section.UserSurvey02.section_idx}
            projectName={projectName}
            clientName={clientName}
            date={date}
            userName={userName}
            signImg={section.UserSurvey02.sign_img}
          />
        )}
        {section.UserSurvey02?.section_flag === 1 && lang === 'ko' && (
          <UserSurvey02
            mode="view"
            idx={section.UserSurvey02.section_idx}
            projectName={projectName}
            clientName={clientName}
            date={date}
            userName={userName}
            signImg={section.UserSurvey02.sign_img}
          />
        )}

        {/* UserSurvey03 */}
        {section.UserSurvey03?.section_flag === 1 && lang === 'en' && (
          <UserSurvey03En mode="view" account={accountData} />
        )}
        {section.UserSurvey03?.section_flag === 1 && lang === 'ko' && (
          <UserSurvey03 mode="view" account={accountData} />
        )}

        {/* UserSurvey04 */}
        {section.UserSurvey04?.section_flag === 1 && lang === 'en' && (
          <UserSurvey04En
            mode="view"
            idx={section.UserSurvey04.section_idx}
            userName={userName}
            projectName={projectName}
            date={date}
            signImg={section.UserSurvey04.sign_img}
          />
        )}
        {section.UserSurvey04?.section_flag === 1 && lang === 'ko' && (
          <UserSurvey04
            mode="view"
            idx={section.UserSurvey04.section_idx}
            userName={userName}
            projectName={projectName}
            date={date}
            signImg={section.UserSurvey04.sign_img}
          />
        )}
      </Box>
    );
  };

  const handleCopyLink = () => {
    if (data?.results?.sign_key) {
      const link = `${window.location.origin}/viewer/${data.results.sign_key}`;
      navigator.clipboard.writeText(link);
      // TODO: Show success notification
    }
  };

  const handlePrint = () => {
    if (data?.results?.sign_key) {
      window.open(`/viewer/${data.results.sign_key}?mode=print`, '_blank');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      pending: { label: '대기중', color: 'warning' as const },
      complete: { label: '완료', color: 'success' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      color: 'default' as const,
    };

    return <Chip label={config.label} color={config.color} />;
  };

  if (isLoading) {
    return <LoadingSpinner message="문서 정보를 불러오는 중..." />;
  }

  if (error || (!data && !isLoading)) {
    console.log('🚫 SignatureDetailPage Error:', {
      error,
      data,
      isLoading,
      idx,
      signKey,
      isAdmin,
    });

    return (
      <Box textAlign="center" py={4}>
        <Alert severity="error">
          문서 정보를 불러오는데 실패했습니다.
          <br />
          {error?.message || '알 수 없는 오류가 발생했습니다.'}
          <br />
          <small>사용자 권한: {isAdmin ? '관리자' : '일반 사용자'}</small>
          <br />
          <small>
            요청 정보: idx={idx}, signKey={signKey}
          </small>
        </Alert>
        <Button
          onClick={() => navigate('/list')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          목록으로 돌아가기
        </Button>
      </Box>
    );
  }

  const results = (data as any)?.results;
  const section = (data as any)?.section_data || [];

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/list')}
          sx={{ mr: 2 }}
        >
          목록으로
        </Button>
        <Typography variant="h4" component="h1">
          문서 상세 정보
        </Typography>
      </Box>

      <DetailContainer elevation={2}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={3}
        >
          <Typography variant="h5">{results.project}</Typography>
          {getStatusChip(results.sign_status)}
        </Box>

        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <InfoSection>
              <Typography variant="h6" gutterBottom>
                프로젝트 정보
              </Typography>
              <InfoRow>
                <span className="label">프로젝트명:</span>
                <span className="value">{results.project}</span>
              </InfoRow>
              <InfoRow>
                <span className="label">클라이언트:</span>
                <span className="value">{results.major_nm}</span>
              </InfoRow>
              <InfoRow>
                <span className="label">문서 유형:</span>
                <span className="value">
                  {results.sign_type === 'sign'
                    ? '사용자조사 개인정보 관련'
                    : 'COVID-19 관련'}
                </span>
              </InfoRow>
              <InfoRow>
                <span className="label">언어:</span>
                <span className="value">
                  {results.language === 'ko' ? '국문' : '영문'}
                </span>
              </InfoRow>
            </InfoSection>
          </Grid>

          <Grid item xs={12} md={6}>
            <InfoSection>
              <Typography variant="h6" gutterBottom>
                참여자 정보
              </Typography>
              <InfoRow>
                <span className="label">참여자명:</span>
                <span className="value">{results.minor_nm}</span>
              </InfoRow>
              <InfoRow>
                <span className="label">담당자:</span>
                <span className="value">{results.officer_nm}</span>
              </InfoRow>
              <InfoRow>
                <span className="label">이메일:</span>
                <span className="value">{results.officer_mail}</span>
              </InfoRow>
            </InfoSection>
          </Grid>
        </Grid>

        <Divider sx={{ my: 3 }} />

        <InfoSection>
          <Typography variant="h6" gutterBottom>
            문서 정보
          </Typography>
          <InfoRow>
            <span className="label">생성일:</span>
            <span className="value">{formatDate(results.create_date)}</span>
          </InfoRow>
          <InfoRow>
            <span className="label">수정일:</span>
            <span className="value">{formatDate(results.update_date)}</span>
          </InfoRow>
        </InfoSection>

        {section && section.length > 0 ? (
          <>
            <Divider sx={{ my: 3 }} />
            <InfoSection>
              <Typography variant="h6" gutterBottom>
                동의서 내용
              </Typography>

              <Typography variant="body2" color="text.secondary" paragraph>
                {results.sign_type === 'sign'
                  ? '사용자조사 개인정보 관련 상세'
                  : 'COVID-19 관련 설문 내용'}
              </Typography>

              {/* 기존 UserSurveyViewContainer 방식으로 렌더링 */}
              {results.sign_type === 'sign' ? (
                <Box>{renderUserSurveyContent()}</Box>
              ) : (
                <Box p={2} bgcolor="warning.50" borderRadius={1}>
                  <Typography variant="body2" color="warning.main">
                    ⚠️ sign_type이 '{results.sign_type}'이므로 UserSurvey
                    컨텐츠를 렌더링하지 않습니다.
                  </Typography>
                </Box>
              )}
              {/* 활성화된 섹션이 없는 경우 */}
              {!section.some((sec: any) => sec.section_flag === 1) && (
                <Box
                  textAlign="center"
                  p={3}
                  bgcolor="grey.100"
                  borderRadius={1}
                >
                  <Typography variant="body2" color="text.secondary">
                    활성화된 섹션이 없습니다.
                  </Typography>
                </Box>
              )}
            </InfoSection>
          </>
        ) : (
          <Box p={2} bgcolor="warning.50" borderRadius={1}>
            <Typography variant="body2" color="warning.main">
              ⚠️ 섹션 데이터가 없거나 비어있습니다. (section:{' '}
              {section ? 'exists' : 'null'}, length:{' '}
              {section ? section.length : 'N/A'})
            </Typography>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />

        <Box display="flex" justifyContent="center" gap={2}>
          <Button
            variant="outlined"
            startIcon={<LinkIcon />}
            onClick={handleCopyLink}
          >
            링크 복사
          </Button>

          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
          >
            인쇄
          </Button>

          {isAdmin && (
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={() => navigate(`/edit/${idx}`)}
            >
              수정
            </Button>
          )}
        </Box>
      </DetailContainer>
    </Box>
  );
});

SignatureDetailPage.displayName = 'SignatureDetailPage';

export default SignatureDetailPage;
