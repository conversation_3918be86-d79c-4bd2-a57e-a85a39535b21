import React, {
  memo,
  useMemo,
  useCallback,
  useState,
  useTransition,
  useEffect,
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Alert,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CreateIcon from '@mui/icons-material/Create';
import {
  useCreateSignature,
  useSignatureDetail,
  useUpdateSignatureAdmin,
} from '@/hooks/useSignatures';
// UserSurvey 컴포넌트들 import
import UserSurvey01 from '@/components/templates/UserSurvey/UserSurvey01';
import UserSurvey01En from '@/components/templates/UserSurvey/UserSurvey01En';
import UserSurvey02 from '@/components/templates/UserSurvey/UserSurvey02';
import UserSurvey02En from '@/components/templates/UserSurvey/UserSurvey02En';
import UserSurvey03 from '@/components/templates/UserSurvey/UserSurvey03';
import UserSurvey03En from '@/components/templates/UserSurvey/UserSurvey03En';
import UserSurvey04 from '@/components/templates/UserSurvey/UserSurvey04';
import UserSurvey04En from '@/components/templates/UserSurvey/UserSurvey04En';
import { useAuth } from '@/hooks/useAuth';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { CreateSignRequest } from '@/types';
import { sessionStorage } from '@/utils/sessionStorage';

const FormContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  maxWidth: 800,
  margin: '0 auto',
}));

const SectionContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
}));

const CheckboxGroup = styled(FormGroup)(({ theme }) => ({
  marginTop: theme.spacing(2),
  '& .MuiFormControlLabel-root': {
    marginBottom: theme.spacing(1),
  },
}));

interface FormData {
  documentType: 'userSurvey' | 'covid19';
  language: 'ko' | 'en';
  projectName: string;
  clientName: string;
  userName: string;
  pxdName: string;
  email: string;
  sections: {
    UserSurvey01: boolean;
    UserSurvey02: boolean;
    UserSurvey03: boolean;
    UserSurvey04: boolean;
  };
}

const CreateSignaturePage: React.FC = memo(() => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user, isAdmin } = useAuth();
  const [isPending, startTransition] = useTransition();
  const [submitError, setSubmitError] = useState<string | null>(null);

  const isEditMode = Boolean(id);
  const createSignatureMutation = useCreateSignature();
  const updateSignatureMutation = useUpdateSignatureAdmin();

  // 수정 모드에서 기존 데이터 로드
  const { data: existingData, isLoading: isLoadingData } = useSignatureDetail(
    id || '',
    'sign',
    '',
    isAdmin
  );

  // 기존 데이터에서 초기값 생성
  const getInitialValues = useMemo(() => {
    if (isEditMode && existingData) {
      const results = (existingData as any)?.results;
      const sectionData = (existingData as any)?.section_data || [];

      return {
        documentType: results?.sign_type === 'sign' ? 'userSurvey' : 'covid19',
        language: results?.language || 'ko',
        projectName: results?.project || '',
        clientName: results?.major_nm || '',
        userName: results?.minor_nm || '',
        pxdName: results?.officer_nm || '',
        email: results?.officer_mail || '',
        sections: {
          UserSurvey01: sectionData[0]?.section_flag === 1,
          UserSurvey02: sectionData[1]?.section_flag === 1,
          UserSurvey03: sectionData[2]?.section_flag === 1,
          UserSurvey04: sectionData[3]?.section_flag === 1,
        },
      };
    }

    // 새 문서 생성 시 기본값
    return {
      documentType: 'userSurvey',
      language: 'ko',
      projectName: '프로젝트명',
      clientName: '클라이언트명',
      userName: '홍길동',
      pxdName: '피엑스디 책임연구원',
      email: '<EMAIL>',
      sections: {
        UserSurvey01: true,
        UserSurvey02: true,
        UserSurvey03: true,
        UserSurvey04: true,
      },
    };
  }, [isEditMode, existingData]);

  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isValid },
  } = useForm<FormData>({
    defaultValues: getInitialValues,
  });

  // 기존 데이터 로드 시 폼 리셋
  useEffect(() => {
    if (isEditMode && existingData) {
      reset(getInitialValues);
    }
  }, [isEditMode, existingData, reset, getInitialValues]);

  const documentType = watch('documentType');
  const sections = watch('sections');
  const language = watch('language');
  const projectName = watch('projectName');
  const clientName = watch('clientName');
  const userName = watch('userName');
  const pxdName = watch('pxdName');
  const email = watch('email');

  // 선택된 섹션 개수 확인
  const selectedSectionsCount = useMemo(() => {
    return Object.values(sections).filter(Boolean).length;
  }, [sections]);

  // 동의서 미리보기 렌더링 함수
  const renderPreview = useCallback(() => {
    if (documentType !== 'userSurvey') return null;

    const currentDate = new Date().toLocaleDateString('ko-KR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    return (
      <Box>
        {/* UserSurvey01 */}
        {sections.UserSurvey01 && language === 'en' && (
          <UserSurvey01En
            mode="view"
            userName={userName}
            pxdName={pxdName}
            email={email}
            date={currentDate}
          />
        )}
        {sections.UserSurvey01 && language === 'ko' && (
          <UserSurvey01
            mode="view"
            userName={userName}
            pxdName={pxdName}
            email={email}
            date={currentDate}
          />
        )}

        {/* UserSurvey02 */}
        {sections.UserSurvey02 && language === 'en' && (
          <UserSurvey02En
            mode="view"
            projectName={projectName}
            clientName={clientName}
            date={currentDate}
            userName={userName}
          />
        )}
        {sections.UserSurvey02 && language === 'ko' && (
          <UserSurvey02
            mode="view"
            projectName={projectName}
            clientName={clientName}
            date={currentDate}
            userName={userName}
          />
        )}

        {/* UserSurvey03 */}
        {sections.UserSurvey03 && language === 'en' && (
          <UserSurvey03En mode="view" />
        )}
        {sections.UserSurvey03 && language === 'ko' && (
          <UserSurvey03 mode="view" />
        )}

        {/* UserSurvey04 */}
        {sections.UserSurvey04 && language === 'en' && (
          <UserSurvey04En
            mode="view"
            userName={userName}
            projectName={projectName}
            date={currentDate}
          />
        )}
        {sections.UserSurvey04 && language === 'ko' && (
          <UserSurvey04
            mode="view"
            userName={userName}
            projectName={projectName}
            date={currentDate}
          />
        )}
      </Box>
    );
  }, [
    documentType,
    sections,
    language,
    projectName,
    clientName,
    userName,
    pxdName,
    email,
  ]);

  const documentTypeOptions = useMemo(
    () => [
      { value: 'userSurvey', label: '사용자조사 개인정보 관련' },
      { value: 'covid19', label: '코로나바이러스 감염증 관련 사실확인 여부' },
    ],
    []
  );

  const languageOptions = useMemo(
    () => [
      { value: 'ko', label: '국문' },
      { value: 'en', label: '영문' },
    ],
    []
  );

  const sectionOptions = useMemo(
    () => [
      { key: 'UserSurvey01', label: 'Intro' },
      { key: 'UserSurvey02', label: '개인정보수집/제공' },
      { key: 'UserSurvey03', label: '사례비 지급' },
      { key: 'UserSurvey04', label: '최종 확인' },
    ],
    []
  );

  const onSubmit = useCallback(
    async (data: FormData) => {
      if (selectedSectionsCount === 0) {
        setSubmitError('최소 1개 이상의 섹션을 선택해주세요.');
        return;
      }

      try {
        setSubmitError(null);

        if (isEditMode) {
          // 수정 모드
          const updateData = {
            pid: parseInt(id!),
            sign_type: data.documentType === 'userSurvey' ? 'sign' : 'survey',
            project: data.projectName,
            major_nm: data.clientName,
            minor_nm: data.userName,
            officer_nm: data.pxdName,
            officer_mail: data.email,
            section_data: Object.entries(data.sections).map(
              ([section_nm, selected], index) => {
                const existingSectionData =
                  (existingData as any)?.section_data || [];
                return {
                  idx: existingSectionData[index]?.section_idx || 0,
                  section_flag: selected ? 1 : 0,
                };
              }
            ),
          };

          await updateSignatureMutation.mutateAsync(updateData);
        } else {
          // 생성 모드
          const requestData: CreateSignRequest = {
            sign_type: data.documentType === 'userSurvey' ? 'sign' : 'survey',
            project: data.projectName,
            major_nm: data.clientName,
            minor_nm: data.userName,
            officer_nm: data.pxdName,
            officer_mail: data.email,
            language: data.language,
            section_data: Object.entries(data.sections).map(
              ([section_nm, selected]) => ({
                section_nm,
                section_flag: selected ? 1 : 0,
              })
            ),
            user_idx:
              user?.user_idx || sessionStorage.get<number>('userIdx') || 0,
          };

          await createSignatureMutation.mutateAsync(requestData);
        }

        startTransition(() => {
          navigate('/list');
        });
      } catch (error: any) {
        setSubmitError(
          error.message ||
            (isEditMode
              ? '문서 수정에 실패했습니다.'
              : '문서 생성에 실패했습니다.')
        );
      }
    },
    [
      selectedSectionsCount,
      createSignatureMutation,
      updateSignatureMutation,
      user,
      navigate,
      isEditMode,
      id,
      existingData,
    ]
  );

  if (isLoadingData) {
    return <LoadingSpinner fullScreen message="문서 정보를 불러오는 중..." />;
  }

  if (createSignatureMutation.isPending || updateSignatureMutation.isPending) {
    return (
      <LoadingSpinner
        fullScreen
        message={isEditMode ? '문서를 수정하는 중...' : '문서를 생성하는 중...'}
      />
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        {isEditMode ? '문서 수정' : '새 문서 생성'}
      </Typography>

      <FormContainer elevation={2}>
        <form onSubmit={handleSubmit(onSubmit)}>
          {submitError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {submitError}
            </Alert>
          )}

          <SectionContainer>
            <Typography variant="h6" gutterBottom>
              문서 설정
            </Typography>

            <Box display="flex" gap={2} mb={2}>
              <FormControl fullWidth>
                <InputLabel>문서 유형</InputLabel>
                <Controller
                  name="documentType"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="문서 유형">
                      {documentTypeOptions.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>언어</InputLabel>
                <Controller
                  name="language"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="언어">
                      {languageOptions.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Box>
          </SectionContainer>

          <Divider sx={{ my: 3 }} />

          <SectionContainer>
            <Typography variant="h6" gutterBottom>
              프로젝트 정보
            </Typography>

            <Box display="flex" flexDirection="column" gap={2}>
              <Controller
                name="projectName"
                control={control}
                rules={{ required: '프로젝트명을 입력해주세요.' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="프로젝트명"
                    error={!!errors.projectName}
                    helperText={errors.projectName?.message}
                    fullWidth
                  />
                )}
              />

              <Controller
                name="clientName"
                control={control}
                rules={{ required: '클라이언트명을 입력해주세요.' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="클라이언트명"
                    error={!!errors.clientName}
                    helperText={errors.clientName?.message}
                    fullWidth
                  />
                )}
              />
            </Box>
          </SectionContainer>

          <Divider sx={{ my: 3 }} />

          <SectionContainer>
            <Typography variant="h6" gutterBottom>
              참여자 정보
            </Typography>

            <Box display="flex" flexDirection="column" gap={2}>
              <Controller
                name="userName"
                control={control}
                rules={{ required: '참여자명을 입력해주세요.' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="참여자명"
                    error={!!errors.userName}
                    helperText={errors.userName?.message}
                    fullWidth
                  />
                )}
              />

              <Controller
                name="pxdName"
                control={control}
                rules={{ required: '담당자명을 입력해주세요.' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="PXD 담당자명"
                    error={!!errors.pxdName}
                    helperText={errors.pxdName?.message}
                    fullWidth
                  />
                )}
              />

              <Controller
                name="email"
                control={control}
                rules={{
                  required: '이메일을 입력해주세요.',
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: '올바른 이메일 형식이 아닙니다.',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="담당자 이메일"
                    type="email"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    fullWidth
                  />
                )}
              />
            </Box>
          </SectionContainer>

          {documentType === 'userSurvey' && (
            <>
              <Divider sx={{ my: 3 }} />

              <SectionContainer>
                <Typography variant="h6" gutterBottom>
                  포함할 섹션 선택
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  문서에 포함할 섹션을 선택해주세요. (최소 1개 이상)
                </Typography>

                <CheckboxGroup>
                  {sectionOptions.map(option => (
                    <Controller
                      key={option.key}
                      name={`sections.${option.key}` as keyof FormData}
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={value}
                              onChange={e => onChange(e.target.checked)}
                            />
                          }
                          label={option.label}
                        />
                      )}
                    />
                  ))}
                </CheckboxGroup>

                <Typography variant="caption" color="text.secondary">
                  선택된 섹션: {selectedSectionsCount}개
                </Typography>
              </SectionContainer>
            </>
          )}

          <Box display="flex" justifyContent="center" gap={2} mt={4}>
            <Button
              variant="outlined"
              onClick={() => navigate('/list')}
              disabled={isPending}
            >
              취소
            </Button>
            <Button
              type="submit"
              variant="contained"
              startIcon={<CreateIcon />}
              disabled={!isValid || isPending || selectedSectionsCount === 0}
            >
              {isEditMode ? '수정' : '생성'}
            </Button>
          </Box>
        </form>
      </FormContainer>

      {/* 동의서 미리보기 섹션 */}
      {documentType === 'userSurvey' && selectedSectionsCount > 0 && (
        <Box sx={{ mt: 4 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
              📋 동의서 미리보기
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              선택한 섹션들의 내용을 미리 확인할 수 있습니다. 실제 문서에서는
              서명 기능이 활성화됩니다.
            </Typography>
            <Divider sx={{ mb: 3 }} />
            {renderPreview()}
          </Paper>
        </Box>
      )}
    </Box>
  );
});

CreateSignaturePage.displayName = 'CreateSignaturePage';

export default CreateSignaturePage;
