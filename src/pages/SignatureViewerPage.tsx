import React, { Suspense, useEffect, useState, useTransition } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { Box, Button, Alert, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import CreateIcon from '@mui/icons-material/Create';
import PrintIcon from '@mui/icons-material/Print';
import { useSignatureViewer, useUpdateSignature } from '@/hooks/useSignatures';
import { useAppDispatch } from '@/store';
import { setHeaderVisibility, setFooterVisibility } from '@/store/slices/uiSlice';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { UpdateSignRequest } from '@/types';

const ViewerContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  backgroundColor: theme.palette.background.default,
  padding: theme.spacing(2),
  '@media print': {
    padding: 0,
    backgroundColor: 'white',
  },
}));

const ViewerPaper = styled(Paper)(({ theme }) => ({
  maxWidth: 800,
  margin: '0 auto',
  padding: theme.spacing(4),
  '@media print': {
    boxShadow: 'none',
    padding: theme.spacing(2),
  },
}));

const ActionButtons = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  gap: theme.spacing(2),
  marginTop: theme.spacing(4),
  '@media print': {
    display: 'none',
  },
}));

// Lazy load viewer components
const UserSurveyViewer = React.lazy(() => import('@/components/viewers/UserSurveyViewer'));
const COVID19Viewer = React.lazy(() => import('@/components/viewers/COVID19Viewer'));

const SignatureViewerPage: React.FC = () => {
  const { key } = useParams<{ key: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [isPending, startTransition] = useTransition();
  
  const [signatureData, setSignatureData] = useState<any>(null);
  const [signImages, setSignImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const mode = searchParams.get('mode') || 'viewer';
  const isPrintMode = mode === 'print';

  const { data: viewerData, isLoading, error } = useSignatureViewer(key || '');
  const updateSignatureMutation = useUpdateSignature();

  useEffect(() => {
    // Hide header/footer for viewer
    dispatch(setHeaderVisibility(false));
    dispatch(setFooterVisibility(false));

    return () => {
      // Restore header/footer when leaving
      dispatch(setHeaderVisibility(true));
      dispatch(setFooterVisibility(true));
    };
  }, [dispatch]);

  useEffect(() => {
    if (isPrintMode && viewerData) {
      // Auto-print after data loads
      setTimeout(() => {
        window.print();
      }, 1000);
    }
  }, [isPrintMode, viewerData]);

  useEffect(() => {
    if (viewerData?.results?.sign_status === 'complete') {
      navigate('/complete');
    }
  }, [viewerData, navigate]);

  const handleSignatureUpdate = (data: any) => {
    startTransition(() => {
      setSignatureData(data);
    });
  };

  const handleSignImageUpdate = (images: string[]) => {
    startTransition(() => {
      setSignImages(images);
    });
  };

  const handleSubmit = async () => {
    if (!viewerData || !key) return;

    try {
      setIsSubmitting(true);

      let submitData: UpdateSignRequest;

      if (viewerData.type === 'sign') {
        // User Survey type
        const sectionList = viewerData.section.map((section, index) => ({
          idx: section.idx,
          sign_img: index === 2 ? null : signImages[index] || null, // Section 2 is account info
        }));

        submitData = {
          idx: viewerData.results.idx,
          type: 'sign',
          account: signatureData,
          sectionList,
        };

        // Validation
        const missingSignatures = viewerData.section
          .map((section, index) => ({ section, index }))
          .filter(({ section, index }) => {
            if (index === 2) {
              // Account info section
              return section.section_flag === 1 && !signatureData;
            } else {
              // Signature sections
              return section.section_flag === 1 && !signImages[index];
            }
          });

        if (missingSignatures.length > 0) {
          const missingSection = missingSignatures[0];
          if (missingSection.index === 2) {
            alert('계좌 정보를 입력해주세요.');
          } else {
            alert(`${missingSection.index + 1}번 섹션에 서명해주세요.`);
          }
          return;
        }
      } else {
        // COVID-19 Survey type
        submitData = {
          idx: viewerData.results.idx,
          type: 'survey',
          sign_img: signImages[0],
          section_idx: viewerData.results.section_idx,
          question: signatureData,
        };

        if (!signImages[0] || !signatureData) {
          alert('모든 항목을 완료해주세요.');
          return;
        }
      }

      await updateSignatureMutation.mutateAsync(submitData);
      navigate('/complete');
    } catch (error: any) {
      alert(error.message || '제출에 실패했습니다.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrint = () => {
    window.open(`/viewer/${key}?mode=print`, '_blank');
  };

  if (isLoading) {
    return <LoadingSpinner fullScreen message="문서를 불러오는 중..." />;
  }

  if (error) {
    return (
      <ViewerContainer>
        <ViewerPaper>
          <Alert severity="error">
            문서를 불러오는데 실패했습니다. 링크를 확인해주세요.
          </Alert>
        </ViewerPaper>
      </ViewerContainer>
    );
  }

  if (!viewerData) {
    return (
      <ViewerContainer>
        <ViewerPaper>
          <Alert severity="warning">
            문서를 찾을 수 없습니다.
          </Alert>
        </ViewerPaper>
      </ViewerContainer>
    );
  }

  const renderViewer = () => {
    const commonProps = {
      data: viewerData,
      mode,
      onDataChange: handleSignatureUpdate,
      onSignImageChange: handleSignImageUpdate,
    };

    if (viewerData.type === 'sign') {
      return (
        <Suspense fallback={<LoadingSpinner message="서명 양식을 불러오는 중..." />}>
          <UserSurveyViewer {...commonProps} />
        </Suspense>
      );
    } else {
      return (
        <Suspense fallback={<LoadingSpinner message="설문 양식을 불러오는 중..." />}>
          <COVID19Viewer {...commonProps} />
        </Suspense>
      );
    }
  };

  return (
    <ErrorBoundary>
      <ViewerContainer className="print-area">
        <ViewerPaper>
          {renderViewer()}
          
          {!isPrintMode && (
            <ActionButtons>
              <Button
                variant="outlined"
                startIcon={<PrintIcon />}
                onClick={handlePrint}
              >
                인쇄
              </Button>
              <Button
                variant="contained"
                startIcon={<CreateIcon />}
                onClick={handleSubmit}
                disabled={isSubmitting || isPending}
              >
                {isSubmitting ? '제출 중...' : '제출'}
              </Button>
            </ActionButtons>
          )}
        </ViewerPaper>
      </ViewerContainer>
    </ErrorBoundary>
  );
};

export default SignatureViewerPage;
