import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Container,
  Paper,
  Box,
  Typography,
  Button,
  Alert,
  Avatar,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import FormField from '@/components/forms/FormField';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { authApi } from '@/api/auth';
import { isValidEmail, isValidUserId } from '@/utils/validation';

const RegisterContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: 'calc(100vh - 200px)',
}));

const RegisterPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  maxWidth: 400,
  width: '100%',
}));

const RegisterForm = styled('form')(({ theme }) => ({
  width: '100%',
  marginTop: theme.spacing(1),
}));

interface RegisterFormData {
  user_id: string;
  user_passwd: string;
  confirmPassword: string;
  user_name: string;
  email: string;
}

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const { control, handleSubmit, watch, formState: { errors } } = useForm<RegisterFormData>({
    defaultValues: {
      user_id: '',
      user_passwd: '',
      confirmPassword: '',
      user_name: '',
      email: '',
    },
  });

  const password = watch('user_passwd');

  const onSubmit = async (data: RegisterFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const { confirmPassword, ...registerData } = data;
      await authApi.register(registerData);
      
      setSuccess(true);
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (error: any) {
      setError(error.message || '회원가입에 실패했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <LoadingSpinner fullScreen message="회원가입 중..." />;
  }

  if (success) {
    return (
      <RegisterContainer maxWidth="sm">
        <RegisterPaper elevation={3}>
          <Alert severity="success" sx={{ width: '100%', mb: 2 }}>
            회원가입이 완료되었습니다. 관리자 승인 후 로그인이 가능합니다.
          </Alert>
          <Typography variant="body2" color="text.secondary">
            잠시 후 로그인 페이지로 이동합니다...
          </Typography>
        </RegisterPaper>
      </RegisterContainer>
    );
  }

  return (
    <RegisterContainer maxWidth="sm">
      <RegisterPaper elevation={3}>
        <Avatar sx={{ m: 1, bgcolor: 'primary.main' }}>
          <PersonAddIcon />
        </Avatar>
        
        <Typography component="h1" variant="h5" gutterBottom>
          회원가입
        </Typography>

        {error && (
          <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
            {error}
          </Alert>
        )}

        <RegisterForm onSubmit={handleSubmit(onSubmit)}>
          <FormField
            name="user_id"
            control={control}
            label="사용자 ID"
            variant="outlined"
            margin="normal"
            required
            autoFocus
            rules={{
              required: '사용자 ID를 입력해주세요.',
              validate: (value) => isValidUserId(value) || '4-20자의 영문자와 숫자만 사용 가능합니다.',
            }}
          />

          <FormField
            name="user_name"
            control={control}
            label="이름"
            variant="outlined"
            margin="normal"
            required
            rules={{
              required: '이름을 입력해주세요.',
              minLength: {
                value: 2,
                message: '이름은 2자 이상이어야 합니다.',
              },
            }}
          />

          <FormField
            name="email"
            control={control}
            label="이메일"
            type="email"
            variant="outlined"
            margin="normal"
            required
            rules={{
              required: '이메일을 입력해주세요.',
              validate: (value) => isValidEmail(value) || '올바른 이메일 형식이 아닙니다.',
            }}
          />

          <FormField
            name="user_passwd"
            control={control}
            label="비밀번호"
            type="password"
            variant="outlined"
            margin="normal"
            required
            rules={{
              required: '비밀번호를 입력해주세요.',
              minLength: {
                value: 8,
                message: '비밀번호는 8자 이상이어야 합니다.',
              },
            }}
          />

          <FormField
            name="confirmPassword"
            control={control}
            label="비밀번호 확인"
            type="password"
            variant="outlined"
            margin="normal"
            required
            rules={{
              required: '비밀번호를 다시 입력해주세요.',
              validate: (value) => value === password || '비밀번호가 일치하지 않습니다.',
            }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={isLoading}
          >
            회원가입
          </Button>

          <Box textAlign="center">
            <Button
              component={Link}
              to="/login"
              variant="text"
              color="primary"
            >
              이미 계정이 있으신가요? 로그인
            </Button>
          </Box>
        </RegisterForm>
      </RegisterPaper>
    </RegisterContainer>
  );
};

export default RegisterPage;
