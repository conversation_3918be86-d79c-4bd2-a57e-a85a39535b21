import React, { memo } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import ListIcon from '@mui/icons-material/List';
import PeopleIcon from '@mui/icons-material/People';
import { useAuth } from '@/hooks/useAuth';

const DashboardContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
}));

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

const ActionCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    backgroundColor: theme.palette.action.hover,
  },
}));

const DashboardPage: React.FC = memo(() => {
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();

  const quickActions = [
    {
      title: '새 문서 생성',
      description: '새로운 서명 문서를 생성합니다',
      icon: <AddIcon sx={{ fontSize: 40 }} />,
      action: () => navigate('/create'),
      color: 'primary.main',
    },
    {
      title: '문서 목록',
      description: '생성된 문서들을 관리합니다',
      icon: <ListIcon sx={{ fontSize: 40 }} />,
      action: () => navigate('/list'),
      color: 'secondary.main',
    },
    ...(isAdmin
      ? [
          {
            title: '사용자 관리',
            description: '시스템 사용자를 관리합니다',
            icon: <PeopleIcon sx={{ fontSize: 40 }} />,
            action: () => navigate('/userList'),
            color: 'success.main',
          },
        ]
      : []),
  ];

  return (
    <DashboardContainer>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          대시보드
        </Typography>
        <Typography variant="body1" color="text.secondary">
          안녕하세요, {user?.user_id || '사용자'}님!
          {isAdmin && ' (관리자)'}
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {quickActions.map((action, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <ActionCard onClick={action.action}>
              <CardContent
                sx={{
                  textAlign: 'center',
                  py: 4,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <Box sx={{ color: action.color }}>{action.icon}</Box>
                <Typography variant="h6" component="h2">
                  {action.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {action.description}
                </Typography>
              </CardContent>
            </ActionCard>
          </Grid>
        ))}
      </Grid>

      <Box mt={6}>
        <Typography variant="h5" gutterBottom>
          최근 활동
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <StatsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  이번 주 생성된 문서
                </Typography>
                <Typography variant="h3" color="primary">
                  0
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  지난 주 대비 0% 증가
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>

          <Grid item xs={12} md={6}>
            <StatsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  완료된 서명
                </Typography>
                <Typography variant="h3" color="success.main">
                  0
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  전체 문서 중 0% 완료
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
        </Grid>
      </Box>

      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          빠른 시작 가이드
        </Typography>
        <Box
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Typography variant="body1" paragraph>
            <strong>1단계:</strong> "새 문서 생성"을 클릭하여 서명 문서를
            만듭니다.
          </Typography>
          <Typography variant="body1" paragraph>
            <strong>2단계:</strong> 프로젝트 정보와 참여자 정보를 입력합니다.
          </Typography>
          <Typography variant="body1" paragraph>
            <strong>3단계:</strong> 생성된 링크를 참여자에게 공유합니다.
          </Typography>
          <Typography variant="body1">
            <strong>4단계:</strong> 참여자가 서명을 완료하면 "문서 목록"에서
            확인할 수 있습니다.
          </Typography>
        </Box>
      </Box>
    </DashboardContainer>
  );
});

DashboardPage.displayName = 'DashboardPage';

export default DashboardPage;
