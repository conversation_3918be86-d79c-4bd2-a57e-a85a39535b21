import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import AddIcon from '@mui/icons-material/Add';

import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LinkIcon from '@mui/icons-material/Link';
import PrintIcon from '@mui/icons-material/Print';
import SearchIcon from '@mui/icons-material/Search';
import { useSignaturesList, useDeleteSignature } from '@/hooks/useSignatures';
import { useAuth } from '@/hooks/useAuth';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ConfirmDialog from '@/components/common/ConfirmDialog';
import { formatDate, formatRelativeTime } from '@/utils/format';
import { SignDocument } from '@/types';

const HeaderSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    gap: theme.spacing(2),
    alignItems: 'stretch',
  },
}));

const FilterSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(3),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
  },
}));

const SignatureListPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAdmin, user } = useAuth();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchType, setSearchType] = useState('project');
  const [searchText, setSearchText] = useState('');
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    document: SignDocument | null;
  }>({ open: false, document: null });

  // Build query params based on user role
  const buildQueryParams = () => {
    if (isAdmin) {
      // 관리자는 모든 데이터 조회
      return searchText
        ? `?search_type=${searchType}&search_text=${searchText}`
        : '';
    } else {
      // 일반 사용자는 자신의 데이터만 조회
      return `?search_type=${searchType}&search_text=${searchText}&user_idx=${user?.user_idx}&is_admin=${user?.is_admin}`;
    }
  };

  const { data, isLoading, error, refetch } = useSignaturesList(
    buildQueryParams()
  );
  const deleteSignatureMutation = useDeleteSignature();

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event?: React.FormEvent) => {
    if (event) event.preventDefault();
    setPage(0);
    refetch();
  };

  const handleSearchEnter = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleDelete = async () => {
    if (!deleteDialog.document) return;

    try {
      await deleteSignatureMutation.mutateAsync(deleteDialog.document.idx);
      setDeleteDialog({ open: false, document: null });
    } catch (error) {
      console.error('Delete failed:', error);
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      false: { label: '대기중', color: 'warning' as const },
      true: { label: '완료', color: 'success' as const },
      pending: { label: '대기중', color: 'warning' as const },
      complete: { label: '완료', color: 'success' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      color: 'default' as const,
    };

    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // TODO: Show success notification
  };

  if (isLoading) {
    return <LoadingSpinner message="문서 목록을 불러오는 중..." />;
  }

  if (error) {
    return (
      <Box textAlign="center" py={4}>
        <Typography color="error">
          문서 목록을 불러오는데 실패했습니다.
          <br />
          {error.message || '네트워크 연결을 확인해주세요.'}
        </Typography>
        <Button onClick={() => refetch()} sx={{ mt: 2 }}>
          다시 시도
        </Button>
      </Box>
    );
  }

  // 안전한 데이터 처리
  const allDocuments = Array.isArray(data?.result)
    ? data.result
    : Array.isArray(data?.data)
    ? data.data
    : Array.isArray(data)
    ? data
    : [];

  // 클라이언트 사이드 페이지네이션
  const documents =
    rowsPerPage > 0
      ? allDocuments.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
      : allDocuments;

  return (
    <Box>
      <HeaderSection>
        <Typography variant="h4" component="h1">
          서명 문서 관리
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/create')}
        >
          새 문서 생성
        </Button>
      </HeaderSection>

      <FilterSection>
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>검색 타입</InputLabel>
          <Select
            value={searchType}
            label="검색 타입"
            onChange={e => setSearchType(e.target.value)}
          >
            <MenuItem value="project">프로젝트명</MenuItem>
            <MenuItem value="officer_nm">담당자</MenuItem>
            <MenuItem value="minor_nm">대상자</MenuItem>
          </Select>
        </FormControl>

        <TextField
          placeholder="검색어를 입력하세요"
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          onKeyPress={handleSearchEnter}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 300 }}
        />

        <Button variant="contained" onClick={handleSearch}>
          검색
        </Button>
      </FilterSection>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>프로젝트명</TableCell>
              <TableCell>클라이언트</TableCell>
              <TableCell>담당자</TableCell>
              <TableCell>상태</TableCell>
              <TableCell>생성일</TableCell>
              <TableCell align="center">작업</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {documents.map(document => (
              <TableRow key={document.idx || Math.random()} hover>
                <TableCell>
                  <Typography
                    component="span"
                    sx={{
                      cursor: 'pointer',
                      color: 'primary.main',
                      textDecoration: 'underline',
                      '&:hover': {
                        color: 'primary.dark',
                      },
                    }}
                    onClick={() =>
                      navigate(`/view?type=sign&idx=${document.idx}`, {
                        state: { signKey: document.url_key },
                      })
                    }
                  >
                    {document.project || '-'}
                  </Typography>
                </TableCell>
                <TableCell>{document.major_nm || '-'}</TableCell>
                <TableCell>{document.officer_nm || '-'}</TableCell>
                <TableCell>{getStatusChip(document.sign_status)}</TableCell>
                <TableCell>
                  {document.create_date ? (
                    <Tooltip title={formatDate(document.create_date)}>
                      <span>{formatRelativeTime(document.create_date)}</span>
                    </Tooltip>
                  ) : (
                    <span>-</span>
                  )}
                </TableCell>
                <TableCell align="center">
                  <Box display="flex" gap={0.5} justifyContent="center">
                    {isAdmin && (
                      <>
                        <Tooltip title="수정">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/edit/${document.idx}`)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="삭제">
                          <IconButton
                            size="small"
                            onClick={() =>
                              setDeleteDialog({ open: true, document })
                            }
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </>
                    )}

                    <Tooltip title="링크 복사">
                      <IconButton
                        size="small"
                        onClick={() =>
                          copyToClipboard(
                            `${window.location.origin}/viewer/${document.url_key}`
                          )
                        }
                      >
                        <LinkIcon />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="인쇄">
                      <IconButton
                        size="small"
                        onClick={() =>
                          window.open(`/viewer/${document.url_key}?mode=print`)
                        }
                      >
                        <PrintIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}

            {documents.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                  <Typography color="text.secondary">
                    등록된 문서가 없습니다.
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, { label: 'All', value: -1 }]}
          component="div"
          count={allDocuments.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      <ConfirmDialog
        open={deleteDialog.open}
        title="문서 삭제"
        message={`"${deleteDialog.document?.project}" 문서를 삭제하시겠습니까?`}
        severity="error"
        confirmText="삭제"
        onConfirm={handleDelete}
        onCancel={() => setDeleteDialog({ open: false, document: null })}
      />
    </Box>
  );
};

export default SignatureListPage;
