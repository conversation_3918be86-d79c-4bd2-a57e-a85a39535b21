import React, {useState} from 'react';
import moment from 'moment';
import 'moment/locale/ko';
import 'react-dates/initialize';
import {DateRangePicker} from 'react-dates';
import 'react-dates/lib/css/_datepicker.css';

moment.locale('ko');

const DatePickerRange = ({ onChange }) => {
	const [startDate, setStartDate] = useState(null);
	const [endDate, setEndDate] = useState(null);
	const [focusedInput, setFocusedInput] = useState(null);

	const handleDatesChange = ({ startDate, endDate }) => {
		setStartDate(startDate);
		setEndDate(endDate);
		onChange({ startDate, endDate });
	};

	return (
		<DateRangePicker
			numberOfMonths={window.innerWidth < 600 ? 1 : 2}
			startDate={startDate}
			startDateId="start-date"
			endDate={endDate}
			endDateId="end-date"
			onDatesChange={handleDatesChange}
			focusedInput={focusedInput}
			onFocusChange={focusedInput => setFocusedInput(focusedInput)}
			displayFormat="YYYY-MM-DD"
			hideKeyboardShortcutsPanel
			// withPortal
			withFullScreenPortal={window.innerWidth < 400}
		/>
	)
}

export default DatePickerRange;
