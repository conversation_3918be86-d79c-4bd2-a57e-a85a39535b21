import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Collapse,
  Chip,
  Divider,
} from '@mui/material';
import { useAuth } from '@/hooks/useAuth';
import { sessionStorage } from '@/utils/sessionStorage';

const AuthDebugger: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, isAuthenticated, isAdmin, isApproved, hasValidToken } = useAuth();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const authData = sessionStorage.getAuthData();

  return (
    <Box position="fixed" top={10} right={10} zIndex={9999}>
      <Button
        variant="outlined"
        size="small"
        onClick={() => setIsOpen(!isOpen)}
        color={isAuthenticated ? 'success' : 'error'}
      >
        🔐 Auth Debug
      </Button>
      
      <Collapse in={isOpen}>
        <Card sx={{ mt: 1, minWidth: 300, maxWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              인증 상태 디버거
            </Typography>
            
            <Box display="flex" gap={1} mb={2} flexWrap="wrap">
              <Chip 
                label={`인증: ${isAuthenticated ? 'O' : 'X'}`}
                color={isAuthenticated ? 'success' : 'error'}
                size="small"
              />
              <Chip 
                label={`관리자: ${isAdmin ? 'O' : 'X'}`}
                color={isAdmin ? 'info' : 'default'}
                size="small"
              />
              <Chip 
                label={`승인: ${isApproved ? 'O' : 'X'}`}
                color={isApproved ? 'success' : 'warning'}
                size="small"
              />
              <Chip 
                label={`토큰: ${hasValidToken ? 'O' : 'X'}`}
                color={hasValidToken ? 'success' : 'error'}
                size="small"
              />
            </Box>

            <Divider sx={{ my: 1 }} />

            <Typography variant="subtitle2" gutterBottom>
              사용자 정보:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
              {user ? JSON.stringify(user, null, 2) : 'null'}
            </Typography>

            <Divider sx={{ my: 1 }} />

            <Typography variant="subtitle2" gutterBottom>
              세션 데이터:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
              Token: {authData.token ? `${authData.token.substring(0, 20)}...` : 'null'}
              <br />
              UserIdx: {authData.userIdx}
              <br />
              IsAdmin: {authData.isAdmin}
              <br />
              UserId: {authData.userId}
              <br />
              IsLogin: {authData.isLogin ? 'true' : 'false'}
            </Typography>

            <Box mt={2} display="flex" gap={1}>
              <Button
                size="small"
                variant="outlined"
                onClick={() => {
                  sessionStorage.clearAuthData();
                  window.location.reload();
                }}
              >
                세션 클리어
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => {
                  console.log('Auth Data:', authData);
                  console.log('User:', user);
                }}
              >
                콘솔 출력
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Collapse>
    </Box>
  );
};

export default AuthDebugger;
