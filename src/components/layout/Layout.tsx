import React from 'react';
import { Box, Container } from '@mui/material';
import { styled } from '@mui/material/styles';
import Header from './Header';
import Footer from './Footer';
import { useAppSelector } from '@/store';

const LayoutRoot = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  minHeight: '100vh',
});

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  paddingTop: theme.spacing(12), // AppBar 높이만큼 여백
  paddingBottom: theme.spacing(4),
}));

const ContentContainer = styled(Container)(({ theme }) => ({
  maxWidth: '1000px',
  padding: theme.spacing(0, 2.5),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(0, 3),
  },
}));

interface LayoutProps {
  children: React.ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  disableGutters?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  maxWidth = 'lg',
  disableGutters = false 
}) => {
  const { header, footer } = useAppSelector(state => state.ui);

  return (
    <LayoutRoot>
      <Header isVisible={header} />
      
      <MainContent component="main">
        {disableGutters ? (
          children
        ) : (
          <ContentContainer maxWidth={maxWidth}>
            {children}
          </ContentContainer>
        )}
      </MainContent>
      
      <Footer isVisible={footer} />
    </LayoutRoot>
  );
};

export default Layout;
