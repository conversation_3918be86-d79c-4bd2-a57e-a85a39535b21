import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import { styled } from '@mui/material/styles';

const FooterContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[100],
  borderTop: `1px solid ${theme.palette.divider}`,
  marginTop: 'auto',
  padding: theme.spacing(3, 0),
}));

interface FooterProps {
  isVisible?: boolean;
}

const Footer: React.FC<FooterProps> = ({ isVisible = true }) => {
  if (!isVisible) return null;

  return (
    <FooterContainer component="footer">
      <Container maxWidth="lg">
        <Box textAlign="center">
          <Typography variant="body2" color="text.secondary">
            © {new Date().getFullYear()} PXD Signature. All rights reserved.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            피엑스디 전자서명 시스템
          </Typography>
        </Box>
      </Container>
    </FooterContainer>
  );
};

export default Footer;
