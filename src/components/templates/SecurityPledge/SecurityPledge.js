import React, { useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
// import TableContainer from '@material-ui/core/TableContainer';
import TableRow from '@material-ui/core/TableRow';
// import Paper from '@material-ui/core/Paper';
import TextField from '@material-ui/core/TextField';
import moment from "moment";
import DatePickerSingle from "../../DatePickerSingle";
import DatePickerRange from "../../DatePickerRange";

const useStyles = makeStyles((theme) => ({
	preview: {
		minHeight: "600px"
	},
}));

const SecurityPledge = (props) => {
	const classes = useStyles();
	const [project, setProject] = useState(null);
	const [master, setMaster] = useState(null);
	const [slave, setSlave] = useState(null);
	const [startDate, setStartDate] = useState(null);
	const [endDate, setEndDate] = useState(null);
	const [date, setDate] = useState(null);

	const onProjectChange = (value) => {

	}

	const onDateChange = (date) => {
		let dateFormat = moment(date).format('LL');
		setDate(dateFormat);
	}

	const onRangeDateChange = ({ startDate, endDate }) => {
		let startDateFormat = moment(startDate).format('LL');
		let endDateFormat = moment(endDate).format('LL');

		if (moment.isMoment(startDate)) setStartDate(startDateFormat);
		if (moment.isMoment(endDate)) setEndDate(endDateFormat);
	}

	return (
		<div>
			<div>
				{/*<TableContainer component={Paper} className={classes.table}>*/}
					<Table aria-label="table">
						<TableBody>
							<TableRow>
								<TableCell component="th" style={{ width: 160 }}>프로젝트 명</TableCell>
								<TableCell>
									<TextField label="프로젝트 이름" onChange={(e) => setProject(e.target.value)} />
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell>갑</TableCell>
								<TableCell>
									<TextField label="이름" onChange={(e) => setMaster(e.target.value)} />
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell>을</TableCell>
								<TableCell>
									<TextField label="이름" onChange={(e) => setSlave(e.target.value)} />
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell>기간</TableCell>
								<TableCell>
									<DatePickerRange onChange={(date) => onRangeDateChange(date)} />
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell>작성일</TableCell>
								<TableCell>
									<DatePickerSingle onChange={(date) => onDateChange(date)}/>
								</TableCell>
							</TableRow>
						</TableBody>
					</Table>
				{/*</TableContainer>*/}
			</div>
			<div className={classes.preview}>
				{props.text}<br/>
				{project}<br/>
				{master}<br/>
				{slave}<br/>
				{startDate}<br/>
				{endDate}<br/>
				{date}<br/>

			</div>
		</div>
	)
}

export default SecurityPledge;
