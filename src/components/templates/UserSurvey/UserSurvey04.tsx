import React, { useEffect, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import CreateIcon from '@mui/icons-material/Create';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const AlignRight = styled(Box)({
  textAlign: 'right'
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

const SignPadContainer = styled(Box)({
  marginTop: '20px',
  textAlign: 'right',
  '& .signature': {
    display: 'inline-block',
    width: '335px',
    height: '200px',
    border: '1px dotted #333'
  }
});

interface UserSurvey04Props {
  mode?: string;
  idx?: number;
  userName?: string;
  projectName?: string;
  date?: string;
  signImg?: string;
}

const UserSurvey04: React.FC<UserSurvey04Props> = ({
  mode = 'view',
  idx,
  userName,
  projectName,
  date,
  signImg
}) => {
  const [signFlag, setSignFlag] = useState(false);

  useEffect(() => {
    if (signImg) {
      setSignFlag(true);
    }
  }, [signImg]);

  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>최종 확인 및 동의서</BoldText>
      </Typography>
      
      <Typography paragraph>
        본인은 사용자조사 참여와 관련된 모든 내용을 확인하였으며, 최종적으로 동의합니다.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>최종 확인 사항</BoldText>
        </Typography>
        <Typography>
          - 개인정보 수집 및 이용에 동의<br/>
          - 사용자조사 참여에 동의<br/>
          - 조사 과정 녹화/녹음에 동의<br/>
          - 연구 결과 활용에 동의<br/>
          - 참여 수당 지급 정보 제공 동의
        </Typography>
      </InfoBox>

      <Typography paragraph sx={{ mt: 2 }}>
        위의 모든 사항에 대해 충분히 설명을 들었으며, 자발적으로 참여에 동의합니다.
      </Typography>

      <Box sx={{ mt: 3, mb: 2 }}>
        <Typography>
          <BoldText>프로젝트:</BoldText> {projectName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>참여자:</BoldText> {userName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>날짜:</BoldText> {date || '_______________'}
        </Typography>
      </Box>

      <AlignRight>
        <Typography gutterBottom>
          최종 동의 서명
        </Typography>
        
        {mode === 'view' && signImg ? (
          <Box sx={{ mt: 2 }}>
            <img 
              src={signImg} 
              alt="서명" 
              style={{ 
                maxWidth: '335px', 
                maxHeight: '200px',
                border: '1px solid #ddd'
              }} 
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              서명: {userName}
            </Typography>
          </Box>
        ) : mode === 'view' ? (
          <Box 
            sx={{ 
              width: '335px', 
              height: '200px', 
              border: '1px dotted #333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              서명이 없습니다
            </Typography>
          </Box>
        ) : (
          <SignPadContainer>
            <canvas
              className="signature"
              width="335px"
              height="200px"
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ClearIcon />}
                sx={{ mr: 1 }}
              >
                지우기
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<CreateIcon />}
              >
                저장
              </Button>
            </Box>
          </SignPadContainer>
        )}
      </AlignRight>
    </SectionContainer>
  );
};

export default UserSurvey04;
