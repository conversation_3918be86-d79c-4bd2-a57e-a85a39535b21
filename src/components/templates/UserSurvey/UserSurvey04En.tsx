import React, { useEffect, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import CreateIcon from '@mui/icons-material/Create';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const AlignRight = styled(Box)({
  textAlign: 'right'
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

const SignPadContainer = styled(Box)({
  marginTop: '20px',
  textAlign: 'right',
  '& .signature': {
    display: 'inline-block',
    width: '335px',
    height: '200px',
    border: '1px dotted #333'
  }
});

interface UserSurvey04EnProps {
  mode?: string;
  idx?: number;
  userName?: string;
  projectName?: string;
  date?: string;
  signImg?: string;
}

const UserSurvey04En: React.FC<UserSurvey04EnProps> = ({
  mode = 'view',
  idx,
  userName,
  projectName,
  date,
  signImg
}) => {
  const [signFlag, setSignFlag] = useState(false);

  useEffect(() => {
    if (signImg) {
      setSignFlag(true);
    }
  }, [signImg]);

  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>Final Confirmation and Consent Form</BoldText>
      </Typography>
      
      <Typography paragraph>
        I have reviewed all contents related to user research participation and give my final consent.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>Final Confirmation Items</BoldText>
        </Typography>
        <Typography>
          - Consent to personal information collection and use<br/>
          - Consent to user research participation<br/>
          - Consent to recording/audio recording of the research process<br/>
          - Consent to research result utilization<br/>
          - Consent to providing participation fee payment information
        </Typography>
      </InfoBox>

      <Typography paragraph sx={{ mt: 2 }}>
        I have received sufficient explanation of all the above matters and voluntarily consent to participate.
      </Typography>

      <Box sx={{ mt: 3, mb: 2 }}>
        <Typography>
          <BoldText>Project:</BoldText> {projectName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>Participant:</BoldText> {userName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>Date:</BoldText> {date || '_______________'}
        </Typography>
      </Box>

      <AlignRight>
        <Typography gutterBottom>
          Final Consent Signature
        </Typography>
        
        {mode === 'view' && signImg ? (
          <Box sx={{ mt: 2 }}>
            <img 
              src={signImg} 
              alt="Signature" 
              style={{ 
                maxWidth: '335px', 
                maxHeight: '200px',
                border: '1px solid #ddd'
              }} 
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              Signature: {userName}
            </Typography>
          </Box>
        ) : mode === 'view' ? (
          <Box 
            sx={{ 
              width: '335px', 
              height: '200px', 
              border: '1px dotted #333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              No signature
            </Typography>
          </Box>
        ) : (
          <SignPadContainer>
            <canvas
              className="signature"
              width="335px"
              height="200px"
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ClearIcon />}
                sx={{ mr: 1 }}
              >
                Clear
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<CreateIcon />}
              >
                Save
              </Button>
            </Box>
          </SignPadContainer>
        )}
      </AlignRight>
    </SectionContainer>
  );
};

export default UserSurvey04En;
