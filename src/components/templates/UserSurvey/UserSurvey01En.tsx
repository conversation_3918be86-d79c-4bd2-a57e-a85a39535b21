import React, { useEffect, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import CreateIcon from '@mui/icons-material/Create';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const AlignRight = styled(Box)({
  textAlign: 'right'
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

const SignPadContainer = styled(Box)({
  marginTop: '20px',
  textAlign: 'right',
  '& .signature': {
    display: 'inline-block',
    width: '335px',
    height: '200px',
    border: '1px dotted #333'
  }
});

interface UserSurvey01EnProps {
  mode?: string;
  idx?: number;
  userName?: string;
  pxdName?: string;
  email?: string;
  date?: string;
  signImg?: string;
}

const UserSurvey01En: React.FC<UserSurvey01EnProps> = ({
  mode = 'view',
  idx,
  userName,
  pxdName,
  email,
  date,
  signImg
}) => {
  const [signFlag, setSignFlag] = useState(false);

  useEffect(() => {
    if (signImg) {
      setSignFlag(true);
    }
  }, [signImg]);

  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>Personal Information Collection and Use Consent Form</BoldText>
      </Typography>
      
      <Typography paragraph>
        Pixel Display (hereinafter referred to as "Company") intends to collect and use personal information 
        as follows for user research purposes. Please read the contents carefully and decide whether to consent.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>1. Purpose of Collection and Use of Personal Information</BoldText>
        </Typography>
        <Typography>
          - Management of user research participants<br/>
          - Conducting user research and analyzing results<br/>
          - Contact for participation fee payment
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>2. Items of Personal Information to be Collected</BoldText>
        </Typography>
        <Typography>
          - Required items: Name, contact information, email<br/>
          - Optional items: Address, occupation, age group
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>3. Retention and Use Period of Personal Information</BoldText>
        </Typography>
        <Typography>
          The collected personal information will be retained for 1 year after completion of the user research, 
          and will be destroyed without delay when the retention period expires.
        </Typography>
      </InfoBox>

      <Typography paragraph sx={{ mt: 2 }}>
        You have the right to refuse consent to the collection and use of the above personal information. 
        However, if you refuse consent, participation in user research may be restricted.
      </Typography>

      <Box sx={{ mt: 3, mb: 2 }}>
        <Typography>
          <BoldText>Participant:</BoldText> {userName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>Manager:</BoldText> {pxdName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>Email:</BoldText> {email || '_______________'}
        </Typography>
        <Typography>
          <BoldText>Date:</BoldText> {date || '_______________'}
        </Typography>
      </Box>

      <AlignRight>
        <Typography gutterBottom>
          I agree to the above contents.
        </Typography>
        
        {mode === 'view' && signImg ? (
          <Box sx={{ mt: 2 }}>
            <img 
              src={signImg} 
              alt="Signature" 
              style={{ 
                maxWidth: '335px', 
                maxHeight: '200px',
                border: '1px solid #ddd'
              }} 
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              Signature: {userName}
            </Typography>
          </Box>
        ) : mode === 'view' ? (
          <Box 
            sx={{ 
              width: '335px', 
              height: '200px', 
              border: '1px dotted #333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              No signature
            </Typography>
          </Box>
        ) : (
          <SignPadContainer>
            <canvas
              className="signature"
              width="335px"
              height="200px"
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ClearIcon />}
                sx={{ mr: 1 }}
              >
                Clear
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<CreateIcon />}
              >
                Save
              </Button>
            </Box>
          </SignPadContainer>
        )}
      </AlignRight>
    </SectionContainer>
  );
};

export default UserSurvey01En;
