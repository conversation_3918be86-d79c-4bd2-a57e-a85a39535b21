import React, { useEffect, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import CreateIcon from '@mui/icons-material/Create';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const AlignRight = styled(Box)({
  textAlign: 'right'
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

const SignPadContainer = styled(Box)({
  marginTop: '20px',
  textAlign: 'right',
  '& .signature': {
    display: 'inline-block',
    width: '335px',
    height: '200px',
    border: '1px dotted #333'
  }
});

interface UserSurvey01Props {
  mode?: string;
  idx?: number;
  userName?: string;
  pxdName?: string;
  email?: string;
  date?: string;
  signImg?: string;
}

const UserSurvey01: React.FC<UserSurvey01Props> = ({
  mode = 'view',
  idx,
  userName,
  pxdName,
  email,
  date,
  signImg
}) => {
  const [signFlag, setSignFlag] = useState(false);

  useEffect(() => {
    if (signImg) {
      setSignFlag(true);
    }
  }, [signImg]);

  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>개인정보 수집 및 이용 동의서</BoldText>
      </Typography>
      
      <Typography paragraph>
        픽셀디스플레이(이하 "회사"라 함)는 사용자조사를 위해 아래와 같이 개인정보를 수집·이용하고자 합니다. 
        내용을 자세히 읽으신 후 동의 여부를 결정하여 주십시오.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>1. 개인정보의 수집 및 이용 목적</BoldText>
        </Typography>
        <Typography>
          - 사용자조사 참여자 관리<br/>
          - 사용자조사 진행 및 결과 분석<br/>
          - 참여 수당 지급을 위한 연락
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>2. 수집하는 개인정보의 항목</BoldText>
        </Typography>
        <Typography>
          - 필수항목: 성명, 연락처, 이메일<br/>
          - 선택항목: 주소, 직업, 연령대
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>3. 개인정보의 보유 및 이용 기간</BoldText>
        </Typography>
        <Typography>
          수집된 개인정보는 사용자조사 완료 후 1년간 보관되며, 
          보관 기간 경과 시 지체 없이 파기됩니다.
        </Typography>
      </InfoBox>

      <Typography paragraph sx={{ mt: 2 }}>
        위의 개인정보 수집·이용에 대한 동의를 거부할 권리가 있습니다. 
        그러나 동의를 거부할 경우 사용자조사 참여가 제한될 수 있습니다.
      </Typography>

      <Box sx={{ mt: 3, mb: 2 }}>
        <Typography>
          <BoldText>참여자:</BoldText> {userName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>담당자:</BoldText> {pxdName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>이메일:</BoldText> {email || '_______________'}
        </Typography>
        <Typography>
          <BoldText>날짜:</BoldText> {date || '_______________'}
        </Typography>
      </Box>

      <AlignRight>
        <Typography gutterBottom>
          위 내용에 동의합니다.
        </Typography>
        
        {mode === 'view' && signImg ? (
          <Box sx={{ mt: 2 }}>
            <img 
              src={signImg} 
              alt="서명" 
              style={{ 
                maxWidth: '335px', 
                maxHeight: '200px',
                border: '1px solid #ddd'
              }} 
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              서명: {userName}
            </Typography>
          </Box>
        ) : mode === 'view' ? (
          <Box 
            sx={{ 
              width: '335px', 
              height: '200px', 
              border: '1px dotted #333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              서명이 없습니다
            </Typography>
          </Box>
        ) : (
          <SignPadContainer>
            <canvas
              className="signature"
              width="335px"
              height="200px"
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ClearIcon />}
                sx={{ mr: 1 }}
              >
                지우기
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<CreateIcon />}
              >
                저장
              </Button>
            </Box>
          </SignPadContainer>
        )}
      </AlignRight>
    </SectionContainer>
  );
};

export default UserSurvey01;
