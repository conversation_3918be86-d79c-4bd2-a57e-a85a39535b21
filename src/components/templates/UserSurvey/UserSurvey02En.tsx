import React, { useEffect, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import CreateIcon from '@mui/icons-material/Create';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const AlignRight = styled(Box)({
  textAlign: 'right'
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

const SignPadContainer = styled(Box)({
  marginTop: '20px',
  textAlign: 'right',
  '& .signature': {
    display: 'inline-block',
    width: '335px',
    height: '200px',
    border: '1px dotted #333'
  }
});

interface UserSurvey02EnProps {
  mode?: string;
  idx?: number;
  projectName?: string;
  clientName?: string;
  date?: string;
  userName?: string;
  signImg?: string;
}

const UserSurvey02En: React.FC<UserSurvey02EnProps> = ({
  mode = 'view',
  idx,
  projectName,
  clientName,
  date,
  userName,
  signImg
}) => {
  const [signFlag, setSignFlag] = useState(false);

  useEffect(() => {
    if (signImg) {
      setSignFlag(true);
    }
  }, [signImg]);

  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>User Research Participation Consent Form</BoldText>
      </Typography>
      
      <Typography paragraph>
        I voluntarily participate in the user research below and consent to the use of information 
        collected during the research process.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>Research Overview</BoldText>
        </Typography>
        <Typography>
          <BoldText>Project Name:</BoldText> {projectName || '_______________'}<br/>
          <BoldText>Client:</BoldText> {clientName || '_______________'}<br/>
          <BoldText>Research Date:</BoldText> {date || '_______________'}
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>Participation Details</BoldText>
        </Typography>
        <Typography>
          - Participation in usability testing<br/>
          - Interview and survey responses<br/>
          - Consent to recording/audio recording of the research process<br/>
          - Consent to data use for result analysis
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>Personal Information Processing Policy</BoldText>
        </Typography>
        <Typography>
          - Collected information will be used only for research purposes<br/>
          - Personally identifiable information will be anonymized<br/>
          - Data will be retained for 1 year after research completion and then destroyed<br/>
          - Participants may discontinue participation at any time
        </Typography>
      </InfoBox>

      <Typography paragraph sx={{ mt: 2 }}>
        I fully understand the above contents and consent to participate in the user research.
      </Typography>

      <Box sx={{ mt: 3, mb: 2 }}>
        <Typography>
          <BoldText>Participant Name:</BoldText> {userName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>Participation Date:</BoldText> {date || '_______________'}
        </Typography>
      </Box>

      <AlignRight>
        <Typography gutterBottom>
          I agree to the above contents and sign.
        </Typography>
        
        {mode === 'view' && signImg ? (
          <Box sx={{ mt: 2 }}>
            <img 
              src={signImg} 
              alt="Signature" 
              style={{ 
                maxWidth: '335px', 
                maxHeight: '200px',
                border: '1px solid #ddd'
              }} 
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              Signature: {userName}
            </Typography>
          </Box>
        ) : mode === 'view' ? (
          <Box 
            sx={{ 
              width: '335px', 
              height: '200px', 
              border: '1px dotted #333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              No signature
            </Typography>
          </Box>
        ) : (
          <SignPadContainer>
            <canvas
              className="signature"
              width="335px"
              height="200px"
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ClearIcon />}
                sx={{ mr: 1 }}
              >
                Clear
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<CreateIcon />}
              >
                Save
              </Button>
            </Box>
          </SignPadContainer>
        )}
      </AlignRight>
    </SectionContainer>
  );
};

export default UserSurvey02En;
