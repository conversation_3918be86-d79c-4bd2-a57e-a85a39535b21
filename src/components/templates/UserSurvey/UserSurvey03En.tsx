import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

interface UserSurvey03EnProps {
  mode?: string;
  account?: any;
}

const UserSurvey03En: React.FC<UserSurvey03EnProps> = ({
  mode = 'view',
  account
}) => {
  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>Participation Fee Payment Information</BoldText>
      </Typography>
      
      <Typography paragraph>
        We collect account information for payment of participation fees for user research.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>Payment Information</BoldText>
        </Typography>
        <Typography>
          - Participation fees will be paid within 1 week after completion of the research<br/>
          - Tax-related matters will be handled according to relevant laws<br/>
          - Account information will be used only for the purpose of fee payment
        </Typography>
      </InfoBox>

      {account && (
        <InfoBox>
          <Typography variant="subtitle2" gutterBottom>
            <BoldText>Account Information</BoldText>
          </Typography>
          <Typography>
            <BoldText>Bank Name:</BoldText> {account.bank_name || '_______________'}<br/>
            <BoldText>Account Number:</BoldText> {account.account_number || '_______________'}<br/>
            <BoldText>Account Holder:</BoldText> {account.account_holder || '_______________'}
          </Typography>
        </InfoBox>
      )}

      <Typography paragraph sx={{ mt: 2 }}>
        The above information will be used only for participation fee payment and will be safely 
        destroyed after payment completion.
      </Typography>
    </SectionContainer>
  );
};

export default UserSurvey03En;
