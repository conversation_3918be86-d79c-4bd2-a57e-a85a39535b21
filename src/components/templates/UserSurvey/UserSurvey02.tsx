import React, { useEffect, useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import CreateIcon from '@mui/icons-material/Create';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const AlignRight = styled(Box)({
  textAlign: 'right'
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

const SignPadContainer = styled(Box)({
  marginTop: '20px',
  textAlign: 'right',
  '& .signature': {
    display: 'inline-block',
    width: '335px',
    height: '200px',
    border: '1px dotted #333'
  }
});

interface UserSurvey02Props {
  mode?: string;
  idx?: number;
  projectName?: string;
  clientName?: string;
  date?: string;
  userName?: string;
  signImg?: string;
}

const UserSurvey02: React.FC<UserSurvey02Props> = ({
  mode = 'view',
  idx,
  projectName,
  clientName,
  date,
  userName,
  signImg
}) => {
  const [signFlag, setSignFlag] = useState(false);

  useEffect(() => {
    if (signImg) {
      setSignFlag(true);
    }
  }, [signImg]);

  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>사용자조사 참여 동의서</BoldText>
      </Typography>
      
      <Typography paragraph>
        본인은 아래 사용자조사에 자발적으로 참여하며, 조사 과정에서 수집되는 정보의 활용에 동의합니다.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>조사 개요</BoldText>
        </Typography>
        <Typography>
          <BoldText>프로젝트명:</BoldText> {projectName || '_______________'}<br/>
          <BoldText>클라이언트:</BoldText> {clientName || '_______________'}<br/>
          <BoldText>조사일자:</BoldText> {date || '_______________'}
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>참여 내용</BoldText>
        </Typography>
        <Typography>
          - 사용성 테스트 참여<br/>
          - 인터뷰 및 설문조사 응답<br/>
          - 조사 과정 녹화/녹음 동의<br/>
          - 결과 분석을 위한 데이터 활용 동의
        </Typography>
      </InfoBox>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>개인정보 처리 방침</BoldText>
        </Typography>
        <Typography>
          - 수집된 정보는 연구 목적으로만 사용됩니다<br/>
          - 개인을 식별할 수 있는 정보는 익명화 처리됩니다<br/>
          - 조사 완료 후 1년간 보관 후 폐기됩니다<br/>
          - 참여자는 언제든지 참여를 중단할 수 있습니다
        </Typography>
      </InfoBox>

      <Typography paragraph sx={{ mt: 2 }}>
        본인은 위 내용을 충분히 이해하였으며, 사용자조사 참여에 동의합니다.
      </Typography>

      <Box sx={{ mt: 3, mb: 2 }}>
        <Typography>
          <BoldText>참여자명:</BoldText> {userName || '_______________'}
        </Typography>
        <Typography>
          <BoldText>참여일자:</BoldText> {date || '_______________'}
        </Typography>
      </Box>

      <AlignRight>
        <Typography gutterBottom>
          위 내용에 동의하며 서명합니다.
        </Typography>
        
        {mode === 'view' && signImg ? (
          <Box sx={{ mt: 2 }}>
            <img 
              src={signImg} 
              alt="서명" 
              style={{ 
                maxWidth: '335px', 
                maxHeight: '200px',
                border: '1px solid #ddd'
              }} 
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              서명: {userName}
            </Typography>
          </Box>
        ) : mode === 'view' ? (
          <Box 
            sx={{ 
              width: '335px', 
              height: '200px', 
              border: '1px dotted #333',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'grey.50'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              서명이 없습니다
            </Typography>
          </Box>
        ) : (
          <SignPadContainer>
            <canvas
              className="signature"
              width="335px"
              height="200px"
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ClearIcon />}
                sx={{ mr: 1 }}
              >
                지우기
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<CreateIcon />}
              >
                저장
              </Button>
            </Box>
          </SignPadContainer>
        )}
      </AlignRight>
    </SectionContainer>
  );
};

export default UserSurvey02;
