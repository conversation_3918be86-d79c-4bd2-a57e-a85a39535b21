import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginTop: '30px',
  padding: '20px 0',
  lineHeight: 1.6,
  borderTop: '1px solid #333',
  '& p': {
    marginTop: '10px'
  }
}));

const BoldText = styled('span')({
  fontWeight: 700
});

const InfoBox = styled(Box)({
  margin: '15px 0',
  padding: '15px',
  border: '1px solid #333'
});

interface UserSurvey03Props {
  mode?: string;
  account?: any;
}

const UserSurvey03: React.FC<UserSurvey03Props> = ({
  mode = 'view',
  account
}) => {
  return (
    <SectionContainer>
      <Typography variant="h6" component="h3" gutterBottom>
        <BoldText>참여 수당 지급 정보</BoldText>
      </Typography>
      
      <Typography paragraph>
        사용자조사 참여에 대한 수당 지급을 위해 계좌 정보를 수집합니다.
      </Typography>

      <InfoBox>
        <Typography variant="subtitle2" gutterBottom>
          <BoldText>수당 지급 안내</BoldText>
        </Typography>
        <Typography>
          - 참여 수당은 조사 완료 후 1주일 내에 지급됩니다<br/>
          - 세금 관련 사항은 관련 법령에 따라 처리됩니다<br/>
          - 계좌 정보는 수당 지급 목적으로만 사용됩니다
        </Typography>
      </InfoBox>

      {account && (
        <InfoBox>
          <Typography variant="subtitle2" gutterBottom>
            <BoldText>계좌 정보</BoldText>
          </Typography>
          <Typography>
            <BoldText>은행명:</BoldText> {account.bank_name || '_______________'}<br/>
            <BoldText>계좌번호:</BoldText> {account.account_number || '_______________'}<br/>
            <BoldText>예금주:</BoldText> {account.account_holder || '_______________'}
          </Typography>
        </InfoBox>
      )}

      <Typography paragraph sx={{ mt: 2 }}>
        위 정보는 참여 수당 지급을 위해서만 사용되며, 지급 완료 후 안전하게 폐기됩니다.
      </Typography>
    </SectionContainer>
  );
};

export default UserSurvey03;
