import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { lightTheme } from '@/theme';
import LoadingSpinner from '../LoadingSpinner';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={lightTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    renderWithTheme(<LoadingSpinner />);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('로딩 중...')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    const customMessage = '데이터를 불러오는 중...';
    renderWithTheme(<LoadingSpinner message={customMessage} />);
    
    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  it('renders with custom size', () => {
    renderWithTheme(<LoadingSpinner size={60} />);
    
    const progressbar = screen.getByRole('progressbar');
    expect(progressbar).toHaveStyle({ width: '60px', height: '60px' });
  });

  it('renders in fullscreen mode', () => {
    renderWithTheme(<LoadingSpinner fullScreen />);
    
    const container = screen.getByRole('progressbar').closest('div');
    expect(container).toHaveStyle({ position: 'fixed' });
  });

  it('does not render message when message is empty', () => {
    renderWithTheme(<LoadingSpinner message="" />);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.queryByText('')).not.toBeInTheDocument();
  });
});
