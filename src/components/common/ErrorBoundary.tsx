import React from 'react';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { Box, Typography, Button, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

const ErrorContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  textAlign: 'center',
  maxWidth: 500,
  margin: '0 auto',
  marginTop: theme.spacing(8),
}));

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <ErrorContainer elevation={3}>
      <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
        <ErrorOutlineIcon color="error" sx={{ fontSize: 64 }} />
        <Typography variant="h5" color="error" gutterBottom>
          오류가 발생했습니다
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          예상치 못한 오류가 발생했습니다. 페이지를 새로고침하거나 잠시 후 다시 시도해주세요.
        </Typography>
        {process.env.NODE_ENV === 'development' && (
          <Typography variant="body2" color="error" sx={{ fontFamily: 'monospace' }}>
            {error.message}
          </Typography>
        )}
        <Button variant="contained" onClick={resetErrorBoundary}>
          다시 시도
        </Button>
      </Box>
    </ErrorContainer>
  );
};

interface ErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children, onError }) => {
  return (
    <ReactErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={onError}
      onReset={() => window.location.reload()}
    >
      {children}
    </ReactErrorBoundary>
  );
};

export default ErrorBoundary;
