import React, { forwardRef } from 'react';
import { Box, Button, Paper, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import ClearIcon from '@mui/icons-material/Clear';
import SaveIcon from '@mui/icons-material/Save';
import UndoIcon from '@mui/icons-material/Undo';
import { useSignaturePad } from '@/hooks/useSignaturePad';

interface SignaturePadComponentProps {
  width?: number;
  height?: number;
  onSave?: (dataURL: string) => void;
  disabled?: boolean;
  title?: string;
  initialData?: string;
}

const SignatureContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: 'center',
}));

const CanvasContainer = styled(Box)(({ theme }) => ({
  border: `1px dashed ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  display: 'inline-block',
  margin: theme.spacing(1, 0),
}));

const ButtonGroup = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(1),
  justifyContent: 'center',
  marginTop: theme.spacing(1),
}));

const SignaturePadComponent = forwardRef<HTMLCanvasElement, SignaturePadComponentProps>(
  ({ width = 400, height = 200, onSave, disabled = false, title, initialData }, ref) => {
    const { canvasRef, isEmpty, clear, save, undo, load } = useSignaturePad({
      width,
      height,
      onSave,
    });

    React.useEffect(() => {
      if (initialData) {
        load(initialData);
      }
    }, [initialData, load]);

    const handleSave = () => {
      const dataURL = save();
      if (dataURL && onSave) {
        onSave(dataURL);
      }
    };

    return (
      <SignatureContainer elevation={1}>
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        
        <CanvasContainer>
          <canvas
            ref={(canvas) => {
              if (canvasRef) {
                (canvasRef as React.MutableRefObject<HTMLCanvasElement | null>).current = canvas;
              }
              if (ref) {
                if (typeof ref === 'function') {
                  ref(canvas);
                } else {
                  ref.current = canvas;
                }
              }
            }}
            style={{
              display: 'block',
              touchAction: 'none',
              cursor: disabled ? 'not-allowed' : 'crosshair',
            }}
          />
        </CanvasContainer>

        <ButtonGroup>
          <Button
            variant="outlined"
            startIcon={<UndoIcon />}
            onClick={undo}
            disabled={disabled || isEmpty}
            size="small"
          >
            실행 취소
          </Button>
          <Button
            variant="outlined"
            startIcon={<ClearIcon />}
            onClick={clear}
            disabled={disabled || isEmpty}
            size="small"
          >
            지우기
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={disabled || isEmpty}
            size="small"
          >
            저장
          </Button>
        </ButtonGroup>

        {isEmpty && !disabled && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            위 영역에 서명해주세요
          </Typography>
        )}
      </SignatureContainer>
    );
  }
);

SignaturePadComponent.displayName = 'SignaturePadComponent';

export default SignaturePadComponent;
