import React, { memo, useMemo, useCallback, startTransition } from 'react';
import { Box, Typography, Divider, TextField } from '@mui/material';
import { styled } from '@mui/material/styles';
import SignaturePadComponent from '@/components/common/SignaturePadComponent';
import { formatDate } from '@/utils/format';
import { SignDetailResponse } from '@/types';

const SectionContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  padding: theme.spacing(3),
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 'bold',
  marginBottom: theme.spacing(2),
  color: theme.palette.primary.main,
}));

const ContentText = styled(Typography)(({ theme }) => ({
  lineHeight: 1.8,
  marginBottom: theme.spacing(2),
  '& p': {
    marginBottom: theme.spacing(1),
  },
}));

interface UserSurveyViewerProps {
  data: SignDetailResponse;
  mode?: string;
  onDataChange?: (data: any) => void;
  onSignImageChange?: (images: string[]) => void;
}

const UserSurveyViewer: React.FC<UserSurveyViewerProps> = memo(
  ({ data, mode = 'viewer', onDataChange, onSignImageChange }) => {
    const { results, section } = data;
    const isViewMode = mode === 'view' || mode === 'print';
    const signImages = useMemo(() => new Array(4).fill(''), []);

    // 계좌 정보 상태
    const [accountInfo, setAccountInfo] = React.useState({
      bankName: '',
      accountNumber: '',
      accountHolder: '',
    });

    const handleAccountInfoChange = useCallback(
      (field: string, value: string) => {
        startTransition(() => {
          const newAccountInfo = { ...accountInfo, [field]: value };
          setAccountInfo(newAccountInfo);
          onDataChange?.(newAccountInfo);
        });
      },
      [accountInfo, onDataChange]
    );

    const handleSignatureSave = useCallback(
      (index: number, signData: string) => {
        startTransition(() => {
          const newSignImages = [...signImages];
          newSignImages[index] = signData;
          onSignImageChange?.(newSignImages);
        });
      },
      [signImages, onSignImageChange]
    );

    const renderSection1 = useMemo(() => {
      if (section[0]?.section_flag !== 1) return null;

      return (
        <SectionContainer>
          <SectionTitle variant="h6">1. 사용자 조사 참여 동의서</SectionTitle>
          <ContentText>
            <p>안녕하세요. 피엑스디(pxd)입니다.</p>
            <p>
              저희는 사용자 경험 개선을 위한 조사를 진행하고 있으며, 귀하의
              소중한 의견을 듣고자 합니다.
            </p>
            <p>
              본 조사는 약 30분 정도 소요되며, 조사 내용은 연구 목적으로만
              사용됩니다.
            </p>
            <p>
              <strong>참여자 정보:</strong>
              <br />
              성명: {results.minor_nm}
              <br />
              담당자: {results.officer_nm}
              <br />
              이메일: {results.officer_mail}
              <br />
              날짜: {formatDate(results.create_date)}
            </p>
          </ContentText>

          {!isViewMode && (
            <SignaturePadComponent
              title="참여자 서명"
              onSave={signData => handleSignatureSave(0, signData)}
              width={350}
              height={150}
            />
          )}

          {isViewMode && section[0]?.sign_img && (
            <Box textAlign="center" mt={2}>
              <img
                src={section[0].sign_img}
                alt="참여자 서명"
                style={{ maxWidth: '300px', border: '1px solid #ddd' }}
              />
              <Typography variant="caption" display="block" mt={1}>
                참여자 서명
              </Typography>
            </Box>
          )}
        </SectionContainer>
      );
    }, [section, results, isViewMode, handleSignatureSave]);

    const renderSection2 = useMemo(() => {
      if (section[1]?.section_flag !== 1) return null;

      return (
        <SectionContainer>
          <SectionTitle variant="h6">
            2. 개인정보 수집 및 이용 동의
          </SectionTitle>
          <ContentText>
            <p>
              <strong>수집하는 개인정보 항목:</strong>
            </p>
            <p>• 필수항목: 성명, 연락처, 이메일</p>
            <p>• 선택항목: 직업, 연령대</p>

            <p>
              <strong>개인정보 수집 및 이용목적:</strong>
            </p>
            <p>• 사용자 조사 진행 및 결과 분석</p>
            <p>• 조사 참여 확인 및 사례비 지급</p>

            <p>
              <strong>개인정보 보유 및 이용기간:</strong>
            </p>
            <p>• 조사 완료 후 1년간 보관 후 파기</p>

            <p>위 개인정보 수집 및 이용에 동의합니다.</p>
          </ContentText>

          {!isViewMode && (
            <SignaturePadComponent
              title="동의 서명"
              onSave={signData => handleSignatureSave(1, signData)}
              width={350}
              height={150}
            />
          )}

          {isViewMode && section[1]?.sign_img && (
            <Box textAlign="center" mt={2}>
              <img
                src={section[1].sign_img}
                alt="동의 서명"
                style={{ maxWidth: '300px', border: '1px solid #ddd' }}
              />
              <Typography variant="caption" display="block" mt={1}>
                동의 서명
              </Typography>
            </Box>
          )}
        </SectionContainer>
      );
    }, [section, isViewMode, handleSignatureSave]);

    const renderSection3 = useMemo(() => {
      if (section[2]?.section_flag !== 1) return null;

      return (
        <SectionContainer>
          <SectionTitle variant="h6">
            3. 사례비 지급을 위한 계좌 정보
          </SectionTitle>
          <ContentText>
            <p>조사 참여에 대한 사례비 지급을 위해 계좌 정보를 입력해주세요.</p>
          </ContentText>

          {!isViewMode ? (
            <Box display="flex" flexDirection="column" gap={2}>
              <TextField
                label="은행명"
                value={accountInfo.bankName}
                onChange={e =>
                  handleAccountInfoChange('bankName', e.target.value)
                }
                fullWidth
              />
              <TextField
                label="계좌번호"
                value={accountInfo.accountNumber}
                onChange={e =>
                  handleAccountInfoChange('accountNumber', e.target.value)
                }
                fullWidth
              />
              <TextField
                label="예금주"
                value={accountInfo.accountHolder}
                onChange={e =>
                  handleAccountInfoChange('accountHolder', e.target.value)
                }
                fullWidth
              />
            </Box>
          ) : (
            <Box>
              <Typography>
                <strong>은행명:</strong> {accountInfo.bankName}
              </Typography>
              <Typography>
                <strong>계좌번호:</strong> {accountInfo.accountNumber}
              </Typography>
              <Typography>
                <strong>예금주:</strong> {accountInfo.accountHolder}
              </Typography>
            </Box>
          )}
        </SectionContainer>
      );
    }, [section, isViewMode, accountInfo, handleAccountInfoChange]);

    const renderSection4 = useMemo(() => {
      if (section[3]?.section_flag !== 1) return null;

      return (
        <SectionContainer>
          <SectionTitle variant="h6">4. 최종 확인 및 서명</SectionTitle>
          <ContentText>
            <p>위의 모든 내용을 확인하였으며, 사용자 조사 참여에 동의합니다.</p>
            <p>
              <strong>참여자:</strong> {results.minor_nm}
              <br />
              <strong>날짜:</strong> {formatDate(results.create_date)}
            </p>
          </ContentText>

          {!isViewMode && (
            <SignaturePadComponent
              title="최종 서명"
              onSave={signData => handleSignatureSave(3, signData)}
              width={350}
              height={150}
            />
          )}

          {isViewMode && section[3]?.sign_img && (
            <Box textAlign="center" mt={2}>
              <img
                src={section[3].sign_img}
                alt="최종 서명"
                style={{ maxWidth: '300px', border: '1px solid #ddd' }}
              />
              <Typography variant="caption" display="block" mt={1}>
                최종 서명
              </Typography>
            </Box>
          )}
        </SectionContainer>
      );
    }, [section, results, isViewMode, handleSignatureSave]);

    return (
      <Box>
        <Box textAlign="center" mb={4}>
          <Typography variant="h4" gutterBottom>
            사용자 조사 참여 동의서
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {results.project}
          </Typography>
          <Divider sx={{ my: 2 }} />
        </Box>

        {renderSection1}
        {renderSection2}
        {renderSection3}
        {renderSection4}
      </Box>
    );
  }
);

UserSurveyViewer.displayName = 'UserSurveyViewer';

export default UserSurveyViewer;
