import React, { memo, useMemo, useCallback, startTransition } from 'react';
import {
  Box,
  Typography,
  Divider,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SignaturePadComponent from '@/components/common/SignaturePadComponent';
import { formatDate } from '@/utils/format';
import { SignDetailResponse, COVID19Question } from '@/types';

const QuestionContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
}));

const QuestionText = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  marginBottom: theme.spacing(2),
  lineHeight: 1.6,
}));

interface COVID19ViewerProps {
  data: SignDetailResponse;
  mode?: string;
  onDataChange?: (data: COVID19Question[]) => void;
  onSignImageChange?: (images: string[]) => void;
}

const COVID19Viewer: React.FC<COVID19ViewerProps> = memo(
  ({ data, mode = 'viewer', onDataChange, onSignImageChange }) => {
    const { results } = data;
    const isViewMode = mode === 'view' || mode === 'print';

    // COVID-19 관련 질문들
    const questions = useMemo(
      () => [
        {
          id: 1,
          question: '최근 14일 이내에 해외여행을 다녀오신 적이 있습니까?',
          answer: '',
        },
        {
          id: 2,
          question:
            '최근 14일 이내에 COVID-19 확진자와 접촉하신 적이 있습니까?',
          answer: '',
        },
        {
          id: 3,
          question: '현재 발열, 기침, 호흡곤란 등의 증상이 있습니까?',
          answer: '',
        },
        {
          id: 4,
          question:
            '최근 14일 이내에 집단감염이 발생한 시설을 방문하신 적이 있습니까?',
          answer: '',
        },
        {
          id: 5,
          question: 'COVID-19 백신을 접종받으셨습니까?',
          answer: '',
        },
      ],
      []
    );

    const [responses, setResponses] =
      React.useState<COVID19Question[]>(questions);

    const handleAnswerChange = useCallback(
      (questionId: number, answer: 'yes' | 'no') => {
        startTransition(() => {
          const newResponses = responses.map(q =>
            q.id === questionId ? { ...q, answer } : q
          );
          setResponses(newResponses);
          onDataChange?.(newResponses);
        });
      },
      [responses, onDataChange]
    );

    const handleSignatureSave = useCallback(
      (signData: string) => {
        startTransition(() => {
          onSignImageChange?.([signData]);
        });
      },
      [onSignImageChange]
    );

    const renderQuestion = useCallback(
      (question: COVID19Question, index: number) => (
        <QuestionContainer key={question.id} elevation={1}>
          <QuestionText>
            {index + 1}. {question.question}
          </QuestionText>

          {!isViewMode ? (
            <FormControl component="fieldset">
              <RadioGroup
                row
                value={question.answer}
                onChange={e =>
                  handleAnswerChange(
                    question.id,
                    e.target.value as 'yes' | 'no'
                  )
                }
              >
                <FormControlLabel value="yes" control={<Radio />} label="예" />
                <FormControlLabel
                  value="no"
                  control={<Radio />}
                  label="아니오"
                />
              </RadioGroup>
            </FormControl>
          ) : (
            <Typography variant="body1" color="primary" fontWeight="bold">
              답변:{' '}
              {question.answer === 'yes'
                ? '예'
                : question.answer === 'no'
                ? '아니오'
                : '미응답'}
            </Typography>
          )}
        </QuestionContainer>
      ),
      [isViewMode, handleAnswerChange]
    );

    return (
      <Box>
        <Box textAlign="center" mb={4}>
          <Typography variant="h4" gutterBottom>
            COVID-19 관련 사실 확인서
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {results.project}
          </Typography>
          <Divider sx={{ my: 2 }} />
        </Box>

        <Box mb={4}>
          <Typography variant="h6" gutterBottom>
            기본 정보
          </Typography>
          <Typography>
            <strong>성명:</strong> {results.minor_nm}
          </Typography>
          <Typography>
            <strong>담당자:</strong> {results.officer_nm}
          </Typography>
          <Typography>
            <strong>이메일:</strong> {results.officer_mail}
          </Typography>
          <Typography>
            <strong>작성일:</strong> {formatDate(results.create_date)}
          </Typography>
        </Box>

        <Box mb={4}>
          <Typography variant="h6" gutterBottom>
            건강 상태 확인
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            다음 질문에 정확히 답변해주시기 바랍니다.
          </Typography>

          {responses.map((question, index) => renderQuestion(question, index))}
        </Box>

        <Box mb={4}>
          <Typography variant="h6" gutterBottom>
            확인 및 서명
          </Typography>
          <Typography variant="body1" paragraph>
            위의 모든 질문에 사실대로 답변하였으며, 허위 기재 시 발생하는 모든
            책임은 본인에게 있음을 확인합니다.
          </Typography>

          {!isViewMode && (
            <SignaturePadComponent
              title="서명"
              onSave={handleSignatureSave}
              width={400}
              height={200}
            />
          )}
        </Box>

        <Box textAlign="center" mt={4}>
          <Typography variant="body2" color="text.secondary">
            작성일: {formatDate(results.create_date)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            작성자: {results.minor_nm}
          </Typography>
        </Box>
      </Box>
    );
  }
);

COVID19Viewer.displayName = 'COVID19Viewer';

export default COVID19Viewer;
