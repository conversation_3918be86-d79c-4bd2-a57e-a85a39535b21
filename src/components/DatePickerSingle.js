import React, {useState} from 'react';
import moment from 'moment';
import 'moment/locale/ko';
import 'react-dates/initialize';
import { SingleDatePicker } from 'react-dates';
import 'react-dates/lib/css/_datepicker.css';

moment.locale('ko');

const DatePickerSingle = ({ onChange }) => {
	const [date, setDate] = useState(moment());
	const [focused, setFocused] = useState(false);

	const handleDatesChange = (date) => {
		setDate(date);
		onChange(date);
	}

	return (
		<SingleDatePicker
			// numberOfMonths={window.innerWidth < 600 ? 1 : 2}
			numberOfMonths={1}
			onDateChange={date => handleDatesChange(date)}
			onFocusChange={({ focused }) => setFocused(focused)}
			focused={focused}
			date={date}
			displayFormat="YYYY-MM-DD"
			// isDayBlocked={m => m.day() === 6 || m.day() === 0}
			hideKeyboardShortcutsPanel
			// withPortal
			withFullScreenPortal={window.innerWidth < 400}
		/>
	)
}

export default DatePickerSingle;
