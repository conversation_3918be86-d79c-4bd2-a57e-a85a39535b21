import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { Box, Alert, Button } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requireAdmin = false,
  redirectTo = '/login',
}) => {
  const {
    isAuthenticated,
    isAdmin,
    isLoading,
    isApproved,
    hasValidToken,
    logout,
    error,
    user,
  } = useAuth();
  const location = useLocation();

  // 디버깅 로그
  console.log('🛡️ ProtectedRoute Debug:', {
    requireAuth,
    requireAdmin,
    isAuthenticated,
    isAdmin,
    isLoading,
    isApproved,
    hasValidToken,
    error,
    pathname: location.pathname,
  });

  // 로딩 중일 때
  if (isLoading) {
    console.log('🔄 Loading auth...');
    return <LoadingSpinner fullScreen message="인증 확인 중..." />;
  }

  // 인증 에러가 있을 때
  if (error && requireAuth) {
    console.log('❌ Auth error:', error);
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="50vh"
        gap={2}
      >
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={logout}>
          다시 로그인
        </Button>
      </Box>
    );
  }

  // 인증이 필요하지 않은 라우트 (로그인, 회원가입 등)
  if (!requireAuth) {
    // 이미 로그인된 사용자가 로그인 페이지에 접근하려 할 때
    if (isAuthenticated && location.pathname === '/login') {
      console.log(
        '🔄 Authenticated user accessing login page, redirecting to /list',
        {
          currentPath: location.pathname,
          isAuthenticated,
        }
      );
      return <Navigate to="/dashboard" replace />;
    }
    return <>{children}</>;
  }

  // 각 조건을 단계별로 확인
  console.log('🔍 ProtectedRoute Step-by-step Check:', {
    step1_hasValidToken: hasValidToken,
    step2_isAuthenticated: isAuthenticated,
    step3_requireAuth: requireAuth,
    step4_shouldCheckToken: !hasValidToken && requireAuth,
    step5_shouldCheckAuth: !isAuthenticated && requireAuth,
  });

  // 토큰이 유효하지 않을 때
  if (!hasValidToken && requireAuth) {
    console.log('🚫 Invalid token, redirecting to login', {
      hasValidToken,
      requireAuth,
      currentPath: location.pathname,
    });
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // 인증이 필요한데 로그인하지 않은 경우
  if (!isAuthenticated && requireAuth) {
    console.log('🚫 Not authenticated, redirecting to login', {
      isAuthenticated,
      requireAuth,
      currentPath: location.pathname,
    });
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // 승인되지 않은 사용자
  if (!isApproved) {
    console.log('🚫 User not approved');
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="50vh"
        gap={2}
      >
        <Alert severity="warning" sx={{ maxWidth: 400 }}>
          관리자 승인 대기중입니다. 승인 후 다시 로그인해주세요.
        </Alert>
        <Button variant="contained" onClick={logout}>
          로그아웃
        </Button>
      </Box>
    );
  }

  // 관리자 권한이 필요한데 관리자가 아닌 경우
  if (requireAdmin && !isAdmin) {
    console.log('🚫 Admin access required but user is not admin:', {
      requireAdmin,
      isAdmin,
      user,
    });
    return <Navigate to="/unauthorized" replace />;
  }

  console.log('✅ ProtectedRoute passed all checks, rendering children', {
    currentPath: location.pathname,
    isAuthenticated,
    isAdmin,
    requireAuth,
    requireAdmin,
  });

  return <>{children}</>;
};

export default ProtectedRoute;
