import { useCallback, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  loginAsync,
  logoutAsync,
  initializeAuth,
  clearError,
} from '@/store/slices/authSlice';
import { sessionStorage, AUTH_KEYS } from '@/utils/sessionStorage';
import { LoginRequest, User } from '@/types';

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated, isLoading, error } = useAppSelector(
    state => state.auth
  );

  // 앱 초기화 시 인증 상태 복원
  useEffect(() => {
    const initAuth = async () => {
      console.log('🔄 Initializing auth on page:', location.pathname);
      const authData = sessionStorage.getAuthData();

      if (authData.isLogin && authData.token) {
        try {
          console.log('🔄 Found auth data, initializing...');
          await dispatch(initializeAuth()).unwrap();
          console.log('🔄 Auth initialization successful');
        } catch (error) {
          console.error('❌ Auth initialization failed:', error);
          sessionStorage.clearAuthData();
        }
      } else {
        console.log('🔄 No auth data found');
      }
    };

    initAuth();
  }, [dispatch, location.pathname]);

  const login = useCallback(
    async (credentials: LoginRequest) => {
      try {
        // 로그인 시도 전에 이전 에러 초기화
        dispatch(clearError());

        const result = await dispatch(loginAsync(credentials)).unwrap();

        // 승인 대기 상태 체크
        if (result.is_apply === 0) {
          throw new Error(
            '관리자 승인 대기중입니다. 승인 후 다시 로그인해주세요.'
          );
        }

        // 로그인 성공 시 리다이렉트 (현재 페이지가 로그인 페이지인 경우만)
        if (location.pathname === '/login') {
          const from = (location.state as any)?.from?.pathname || '/dashboard';
          console.log(
            '🔄 Login success, redirecting from login page to:',
            from
          );
          navigate(from, { replace: true });
        } else {
          console.log(
            '🔄 Login success, staying on current page:',
            location.pathname
          );
        }

        return result;
      } catch (error: any) {
        console.error('Login failed:', error);
        throw error;
      }
    },
    [dispatch, navigate, location]
  );

  const logout = useCallback(async () => {
    try {
      await dispatch(logoutAsync()).unwrap();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // 로그아웃 실패해도 로컬 상태는 클리어
      sessionStorage.clearAuthData();
      navigate('/login', { replace: true });
    }
  }, [dispatch, navigate]);

  const initialize = useCallback(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // 토큰 유효성 검사
  const isTokenValid = useCallback(() => {
    const token = sessionStorage.get<string>(AUTH_KEYS.TOKEN);
    if (!token) {
      console.log('🔍 No token found');
      return false;
    }

    try {
      // JWT 토큰 디코딩 (간단한 만료 시간 체크)
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      const isValid = payload.exp > currentTime;

      console.log('🔍 Token validation:', {
        hasToken: !!token,
        tokenExp: payload.exp,
        currentTime,
        isValid,
      });

      return isValid;
    } catch (error) {
      console.error('❌ Token validation failed:', error);
      return false;
    }
  }, []);

  // 권한 체크 헬퍼 함수들
  const isAdmin = user?.is_admin === 1;
  const isApproved = user?.is_apply === 1;
  const hasValidToken = isTokenValid();

  // 디버깅 로그 (개발 환경에서만)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Auth Status Debug:', {
      user,
      isAuthenticated,
      isAdmin,
      isApproved,
      hasValidToken,
      userIsApply: user?.is_apply,
      userIsAdmin: user?.is_admin,
    });
  }

  // 인증 상태 종합 판단 - 토큰 기반으로 더 관대하게 설정
  const isFullyAuthenticated = hasValidToken; // 임시로 토큰만으로 판단

  console.log('🔍 useAuth Final State:', {
    isAuthenticated: isAuthenticated,
    hasValidToken: hasValidToken,
    isFullyAuthenticated: isFullyAuthenticated,
    isAdmin: isAdmin,
    isApproved: isApproved,
    isLoading: isLoading,
    error: error,
    user: user,
  });

  return {
    // 상태
    user,
    isAuthenticated: isFullyAuthenticated,
    isLoading,
    error,

    // 권한
    isAdmin,
    isApproved,
    hasValidToken,

    // 액션
    login,
    logout,
    initialize,
    clearError: clearAuthError,
    isTokenValid,
  };
};
