import { useRef, useEffect, useCallback, useState } from 'react';
import SignaturePad from 'signature_pad';

interface UseSignaturePadOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  penColor?: string;
  onSave?: (dataURL: string) => void;
}

export const useSignaturePad = (options: UseSignaturePadOptions = {}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const signaturePadRef = useRef<SignaturePad | null>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  const {
    width = 400,
    height = 200,
    backgroundColor = 'rgba(255,255,255,0)',
    penColor = '#000000',
    onSave,
  } = options;

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const signaturePad = new SignaturePad(canvas, {
      backgroundColor,
      penColor,
      minWidth: 1,
      maxWidth: 3,
    });

    signaturePadRef.current = signaturePad;

    // 캔버스 크기 설정
    const resizeCanvas = () => {
      const ratio = Math.max(window.devicePixelRatio || 1, 1);
      canvas.width = width * ratio;
      canvas.height = height * ratio;
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;
      
      const context = canvas.getContext('2d');
      if (context) {
        context.scale(ratio, ratio);
      }
      
      signaturePad.clear();
    };

    resizeCanvas();

    // 서명 시작/종료 이벤트
    const handleBegin = () => setIsEmpty(false);
    const handleEnd = () => {
      if (signaturePad.isEmpty()) {
        setIsEmpty(true);
      }
    };

    signaturePad.addEventListener('beginStroke', handleBegin);
    signaturePad.addEventListener('endStroke', handleEnd);

    return () => {
      signaturePad.removeEventListener('beginStroke', handleBegin);
      signaturePad.removeEventListener('endStroke', handleEnd);
      signaturePad.off();
    };
  }, [width, height, backgroundColor, penColor]);

  const clear = useCallback(() => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
      setIsEmpty(true);
    }
  }, []);

  const save = useCallback(() => {
    if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
      return null;
    }

    const dataURL = signaturePadRef.current.toDataURL('image/png');
    onSave?.(dataURL);
    return dataURL;
  }, [onSave]);

  const load = useCallback((dataURL: string) => {
    if (!signaturePadRef.current) return;

    signaturePadRef.current.fromDataURL(dataURL);
    setIsEmpty(false);
  }, []);

  const undo = useCallback(() => {
    if (!signaturePadRef.current) return;

    const data = signaturePadRef.current.toData();
    if (data.length > 0) {
      data.pop();
      signaturePadRef.current.fromData(data);
      setIsEmpty(data.length === 0);
    }
  }, []);

  return {
    canvasRef,
    isEmpty,
    clear,
    save,
    load,
    undo,
  };
};
