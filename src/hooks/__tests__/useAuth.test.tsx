import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import authSlice from '@/store/slices/authSlice';
import uiSlice from '@/store/slices/uiSlice';
import signaturesSlice from '@/store/slices/signaturesSlice';
import { useAuth } from '../useAuth';

// Mock navigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock API
jest.mock('@/api/auth', () => ({
  authApi: {
    login: jest.fn(),
    logout: jest.fn(),
    verifyToken: jest.fn(),
  },
}));

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
      ui: uiSlice,
      signatures: signaturesSlice,
    },
    preloadedState: initialState,
  });
};

const createWrapper = (store: any) => {
  return ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </Provider>
  );
};

describe('useAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns initial auth state', () => {
    const store = createTestStore();
    const wrapper = createWrapper(store);
    
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.isAdmin).toBe(false);
    expect(result.current.isApproved).toBe(false);
  });

  it('returns authenticated state when user is logged in', () => {
    const initialState = {
      auth: {
        user: {
          user_idx: 1,
          user_id: 'testuser',
          is_admin: 1,
          is_apply: 1,
          status: 'success' as const,
        },
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    };
    
    const store = createTestStore(initialState);
    const wrapper = createWrapper(store);
    
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    expect(result.current.user).toBeDefined();
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.isAdmin).toBe(true);
    expect(result.current.isApproved).toBe(true);
  });

  it('provides login function', () => {
    const store = createTestStore();
    const wrapper = createWrapper(store);
    
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    expect(typeof result.current.login).toBe('function');
  });

  it('provides logout function', () => {
    const store = createTestStore();
    const wrapper = createWrapper(store);
    
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    expect(typeof result.current.logout).toBe('function');
  });

  it('calls navigate on logout', async () => {
    const store = createTestStore();
    const wrapper = createWrapper(store);
    
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    await act(async () => {
      await result.current.logout();
    });
    
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });
});
