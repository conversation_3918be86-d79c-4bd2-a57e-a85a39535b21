import { useState, useEffect, useDeferredValue } from 'react';

/**
 * React 18의 useDeferredValue를 활용한 디바운스 훅
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const deferredValue = useDeferredValue(debouncedValue);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return deferredValue;
};

/**
 * 검색어 디바운스 훅
 */
export const useSearchDebounce = (searchTerm: string, delay = 300) => {
  return useDebounce(searchTerm, delay);
};
