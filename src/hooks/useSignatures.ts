import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { signaturesApi } from '@/api/signatures';
import {
  SignDocument,
  SignDetailResponse,
  CreateSignRequest,
  UpdateSignRequest,
} from '@/types';

// Query keys
export const QUERY_KEYS = {
  signatures: ['signatures'] as const,
  signaturesList: (params?: string) => ['signatures', 'list', params] as const,
  signatureDetail: (params: string) =>
    ['signatures', 'detail', params] as const,
  signatureViewer: (key: string) => ['signatures', 'viewer', key] as const,
};

// Hooks
export const useSignaturesList = (queryParams = '') => {
  return useQuery({
    queryKey: QUERY_KEYS.signaturesList(queryParams),
    queryFn: async () => {
      const response = await signaturesApi.getList(queryParams);

      // 개발 환경에서 응답 데이터 구조 확인
      if (process.env.NODE_ENV === 'development') {
        console.log('📋 Signatures List Response:', response);
        if (response.result && response.result.length > 0) {
          console.log('📋 First Document Sample:', response.result[0]);
        }
      }

      return response;
    },
    staleTime: 5 * 60 * 1000, // 5분
    gcTime: 10 * 60 * 1000, // 10분
    retry: 2,
    // 검색 조건이 변경될 때만 새로 요청
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};

export const useSignatureDetail = (
  id: string,
  type: string = 'sign',
  signKey: string = '',
  isAdmin: boolean = false
) => {
  return useQuery({
    queryKey: QUERY_KEYS.signatureDetail(id),
    queryFn: async () => {
      let response;

      console.log('🔍 API Call Debug:', { id, type, signKey, isAdmin });

      try {
        if (id) {
          // type과 idx 파라미터 모두 포함
          response = await signaturesApi.getDetail(`?type=${type}&idx=${id}`);
        } else if (signKey) {
          // sign_key로 사용자 API 시도
          response = await signaturesApi.getDetailForUser(signKey);
        } else {
          throw new Error('idx 또는 signKey가 필요합니다.');
        }
      } catch (error: any) {
        console.log('🔄 Admin API failed, trying user API with signKey...');

        // 관리자 API 실패 시 사용자 API로 재시도 (signKey가 있는 경우)
        if (signKey) {
          response = await signaturesApi.getDetailForUser(signKey);
        } else {
          throw error; // signKey도 없으면 원래 에러 던지기
        }
      }

      // 개발 환경에서 응답 데이터 구조 확인
      if (process.env.NODE_ENV === 'development') {
        console.log('📄 Signature Detail Response:', response);
        console.log('📄 Request Info:', { id, signKey, isAdmin });
      }

      return response;
    },
    enabled: !!id || !!signKey,
    staleTime: 5 * 60 * 1000,
  });
};

export const useSignatureViewer = (key: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.signatureViewer(key),
    queryFn: () => signaturesApi.getDetailForUser(key),
    enabled: !!key,
    staleTime: 0, // 항상 최신 데이터
    retry: 1,
  });
};

export const useCreateSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSignRequest) => signaturesApi.createSign(data),
    onSuccess: () => {
      // 목록 쿼리 무효화
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.signatures,
      });
    },
  });
};

export const useUpdateSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateSignRequest) => signaturesApi.updateUserSign(data),
    onSuccess: (_, variables) => {
      // 관련 쿼리들 무효화
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.signatures,
      });

      // 특정 문서 쿼리 무효화
      if (variables.idx) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.signatureDetail(variables.idx.toString()),
        });
      }
    },
  });
};

// 관리자용 문서 수정 훅
export const useUpdateSignatureAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => signaturesApi.updateSign(data),
    onSuccess: (_, variables) => {
      // 관련 쿼리들 무효화
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.signatures,
      });

      // 특정 문서 쿼리 무효화
      if (variables.pid) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.signatureDetail(variables.pid.toString()),
        });
      }
    },
  });
};

export const useDeleteSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (idx: number) => signaturesApi.deleteSign({ idx }),
    onSuccess: () => {
      // 목록 쿼리 무효화
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.signatures,
      });
    },
  });
};

// 커스텀 훅: 서명 이미지 관리
export const useSignatureImages = () => {
  const queryClient = useQueryClient();

  const saveSignImage = (index: number, imageData: string) => {
    const currentImages =
      queryClient.getQueryData<string[]>(['signImages']) || [];
    const newImages = [...currentImages];
    newImages[index] = imageData;

    queryClient.setQueryData(['signImages'], newImages);
  };

  const getSignImages = (): string[] => {
    return queryClient.getQueryData<string[]>(['signImages']) || [];
  };

  const clearSignImages = () => {
    queryClient.setQueryData(['signImages'], []);
  };

  return {
    saveSignImage,
    getSignImages,
    clearSignImages,
  };
};
