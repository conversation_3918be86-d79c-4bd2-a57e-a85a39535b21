# Node.js 버전 업그레이드 가이드

## 🚨 중요: Node.js 버전 업데이트 필요

이 프로젝트는 React 18과 최신 라이브러리들로 업그레이드되었으며, **Node.js 18.x 이상**이 필요합니다.

## 현재 요구사항

- **Node.js**: 18.0.0 이상 (권장: 18.18.2)
- **npm**: 8.0.0 이상

## 왜 업그레이드가 필요한가?

### 기존 Node.js 14의 문제점
- ❌ **EOL (End of Life)**: 2023년 4월 30일부로 지원 종료
- ❌ **보안 취약점**: 더 이상 보안 패치 제공 안됨
- ❌ **성능 이슈**: 최신 라이브러리들이 Node.js 16+ 최적화
- ❌ **호환성 문제**: React 18, TypeScript 5.x 등과 호환성 이슈

### 사용 중인 라이브러리들의 Node.js 요구사항
| 라이브러리 | 최소 Node.js 버전 |
|-----------|------------------|
| React 18 | 16.14.0+ |
| TypeScript 5.3 | 16.0.0+ |
| React Scripts 5.0.1 | 16.0.0+ |
| @tanstack/react-query 5.x | 16.0.0+ |
| Material-UI v5 | 16.0.0+ |

## 업그레이드 방법

### 1. nvm 사용 (권장)

```bash
# 현재 Node.js 버전 확인
node --version

# nvm이 설치되어 있다면
nvm install 18.18.2
nvm use 18.18.2

# 기본 버전으로 설정
nvm alias default 18.18.2

# 프로젝트 디렉토리에서 자동으로 버전 전환
nvm use
```

### 2. 직접 설치

1. [Node.js 공식 사이트](https://nodejs.org/)에서 LTS 버전 다운로드
2. 설치 후 터미널 재시작
3. 버전 확인: `node --version`

### 3. 패키지 매니저 사용

#### macOS (Homebrew)
```bash
brew install node@18
```

#### Windows (Chocolatey)
```bash
choco install nodejs --version=18.18.2
```

#### Ubuntu/Debian
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 업그레이드 후 확인

### 1. 버전 확인
```bash
# Node.js 버전 확인
node --version  # v18.18.2 이상이어야 함

# npm 버전 확인
npm --version   # 8.x 이상이어야 함
```

### 2. 자동 버전 체크
```bash
# 프로젝트에서 제공하는 버전 체크 스크립트 실행
npm run check-versions
```

### 3. 의존성 재설치
```bash
# 기존 node_modules 삭제
rm -rf node_modules package-lock.json

# 새로운 Node.js 버전으로 재설치
npm install
```

## 문제 해결

### npm 버전이 낮은 경우
```bash
npm install -g npm@latest
```

### nvm이 없는 경우
```bash
# macOS/Linux
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Windows
# https://github.com/coreybutler/nvm-windows 에서 다운로드
```

### 권한 문제 (macOS/Linux)
```bash
# npm 글로벌 패키지 권한 설정
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

## 성능 개선 효과

Node.js 18로 업그레이드하면 다음과 같은 개선을 기대할 수 있습니다:

- 🚀 **빌드 속도 20-30% 향상**
- 🔒 **최신 보안 패치 적용**
- ⚡ **런타임 성능 개선**
- 🛠 **최신 개발 도구 지원**
- 🔧 **더 나은 디버깅 경험**

## 주의사항

1. **기존 프로젝트**: 다른 프로젝트가 Node.js 14에 의존하는 경우 nvm 사용 권장
2. **CI/CD**: GitHub Actions, GitLab CI 등의 Node.js 버전도 함께 업데이트 필요
3. **Docker**: Dockerfile의 Node.js 베이스 이미지도 업데이트 필요

## 추가 리소스

- [Node.js 공식 문서](https://nodejs.org/docs/)
- [nvm 사용법](https://github.com/nvm-sh/nvm)
- [Node.js 버전 지원 정책](https://nodejs.org/en/about/releases/)
- [React 18 시스템 요구사항](https://react.dev/learn/installation)
