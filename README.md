# PXD Signature

현대적인 React 18 기반의 전자 서명 시스템입니다.

## 🚀 주요 기능

- **전자 서명**: 사용자 조사 및 COVID-19 관련 문서에 대한 전자 서명
- **문서 관리**: 서명 문서 생성, 수정, 삭제 및 상태 관리
- **사용자 관리**: 관리자 권한 기반 사용자 승인 시스템
- **반응형 디자인**: 모든 디바이스에서 최적화된 사용자 경험
- **다국어 지원**: 한국어/영어 문서 템플릿

## 🛠 기술 스택

### Frontend
- **React 18** - 최신 React 기능 (Suspense, Concurrent Features)
- **TypeScript** - 타입 안정성
- **Material-UI v5** - 현대적인 UI 컴포넌트
- **Redux Toolkit** - 효율적인 상태 관리
- **React Query** - 서버 상태 관리
- **React Hook Form** - 폼 관리
- **React Router v6** - 라우팅

### Development Tools
- **ESLint + Prettier** - 코드 품질 관리
- **Husky + lint-staged** - Git hooks
- **Storybook** - 컴포넌트 개발
- **Jest + Testing Library** - 테스트

## 📦 설치 및 실행

### 요구사항
- **Node.js 16.14.0 이상** (권장: 16.20.2)
- **npm 8.x 이상** 또는 yarn

> ⚠️ **중요**: 기존 Node.js 14에서 업그레이드가 필요합니다.
> 자세한 업그레이드 가이드는 [Node.js 버전 업그레이드 가이드](docs/NODE_VERSION_UPGRADE.md)를 참조하세요.

### Node.js 버전 확인 및 업그레이드
```bash
# 현재 Node.js 버전 확인
node --version

# nvm 사용 시 (권장)
nvm use
# 또는 특정 버전 설치
nvm install 18.18.2
nvm use 18.18.2

# nvm이 없다면 공식 사이트에서 다운로드
# https://nodejs.org/
```

### 설치
```bash
# 저장소 클론
git clone https://gitlab.com/pxd4group/pxd-signature.git
cd pxd-signature

# Node.js 버전 확인 (18.x 이상이어야 함)
node --version

# 의존성 설치
npm install

# 환경 변수 설정
cp .env.example .env
```

### 개발 서버 실행
```bash
npm start
```

### 빌드
```bash
npm run build
```

### 테스트
```bash
npm test
```

### 코드 품질 검사
```bash
# ESLint 실행
npm run lint

# Prettier 포맷팅
npm run format

# 타입 체크
npm run type-check
```

## 📁 프로젝트 구조

```
src/
├── api/                 # API 클라이언트
├── components/          # 재사용 가능한 컴포넌트
│   ├── common/         # 공통 컴포넌트
│   ├── forms/          # 폼 컴포넌트
│   ├── layout/         # 레이아웃 컴포넌트
│   └── viewers/        # 문서 뷰어 컴포넌트
├── hooks/              # 커스텀 훅
├── pages/              # 페이지 컴포넌트
├── router/             # 라우팅 설정
├── store/              # Redux 스토어
├── theme/              # MUI 테마
├── types/              # TypeScript 타입 정의
└── utils/              # 유틸리티 함수
```

## 🔧 주요 개선사항

### 기술적 개선
- **React 16 → 18**: 최신 React 기능 활용
- **JavaScript → TypeScript**: 타입 안정성 확보
- **Material-UI v4 → v5**: 최신 디자인 시스템
- **Redux → Redux Toolkit**: 보일러플레이트 코드 감소
- **Class Components → Function Components**: 현대적인 React 패턴

### 성능 최적화
- **Code Splitting**: 페이지별 지연 로딩
- **React.memo**: 불필요한 리렌더링 방지
- **useMemo/useCallback**: 메모이제이션 최적화
- **React Query**: 효율적인 데이터 캐싱

### 개발 경험 개선
- **ESLint + Prettier**: 일관된 코드 스타일
- **Husky**: Git commit 시 자동 검사
- **TypeScript**: 개발 시 타입 오류 방지
- **Error Boundary**: 런타임 오류 처리

## 🌟 새로운 기능

### React 18 기능
- **Suspense**: 컴포넌트 지연 로딩
- **useTransition**: 논블로킹 상태 업데이트
- **useDeferredValue**: 성능 최적화된 검색

### 현대적인 UI/UX
- **반응형 디자인**: 모바일 최적화
- **다크 모드**: 테마 전환 지원
- **접근성**: ARIA 속성 및 키보드 네비게이션
- **로딩 상태**: 사용자 피드백 개선

## 📝 라이선스

이 프로젝트는 PXD의 소유입니다.

## 🤝 기여

프로젝트 개선을 위한 기여를 환영합니다. 이슈나 풀 리퀘스트를 통해 참여해주세요.
