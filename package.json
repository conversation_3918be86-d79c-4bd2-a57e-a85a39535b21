{"name": "pxd-signature", "version": "0.2.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@reduxjs/toolkit": "^1.9.7", "@tanstack/react-query": "^5.83.0", "axios": "^1.6.2", "clipboard": "^2.0.11", "date-fns": "^4.1.0", "moment": "^2.29.4", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-input-mask": "^2.0.4", "react-redux": "^8.1.3", "react-router-dom": "^6.19.0", "signature_pad": "^4.1.7"}, "devDependencies": {"@types/qs": "^6.9.10", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "jsdom": "^22.1.0", "typescript": "^5.3.2", "vite": "^4.5.0", "vitest": "^0.34.6"}}