#!/usr/bin/env node

const semver = require('semver');
const packageJson = require('../package.json');

const currentNodeVersion = process.version;
const requiredNodeVersion = packageJson.engines.node;

console.log('🔍 Node.js 버전 확인 중...');
console.log(`현재 Node.js 버전: ${currentNodeVersion}`);
console.log(`요구되는 Node.js 버전: ${requiredNodeVersion}`);

if (!semver.satisfies(currentNodeVersion, requiredNodeVersion)) {
  console.error('\n❌ Node.js 버전이 요구사항을 만족하지 않습니다!');
  console.error(`\n현재 버전: ${currentNodeVersion}`);
  console.error(`요구 버전: ${requiredNodeVersion}`);
  console.error('\n해결 방법:');
  console.error('1. nvm을 사용하는 경우:');
  console.error('   nvm install 16.20.2');
  console.error('   nvm use 16.20.2');
  console.error('\n2. 직접 설치하는 경우:');
  console.error('   https://nodejs.org/ 에서 Node.js 16.x LTS 다운로드');
  console.error('\n3. 프로젝트 디렉토리에서:');
  console.error('   nvm use (이미 .nvmrc 파일이 있는 경우)');
  
  process.exit(1);
}

console.log('✅ Node.js 버전이 요구사항을 만족합니다!');

// npm 버전도 확인
const { execSync } = require('child_process');
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  const requiredNpmVersion = packageJson.engines.npm;
  
  console.log(`현재 npm 버전: ${npmVersion}`);
  console.log(`요구되는 npm 버전: ${requiredNpmVersion}`);
  
  if (!semver.satisfies(npmVersion, requiredNpmVersion)) {
    console.warn('\n⚠️  npm 버전이 요구사항을 만족하지 않습니다.');
    console.warn('npm을 업데이트하려면: npm install -g npm@latest');
  } else {
    console.log('✅ npm 버전도 요구사항을 만족합니다!');
  }
} catch (error) {
  console.warn('⚠️  npm 버전을 확인할 수 없습니다.');
}

console.log('\n🚀 모든 버전 요구사항이 충족되었습니다. 개발을 시작할 수 있습니다!');
